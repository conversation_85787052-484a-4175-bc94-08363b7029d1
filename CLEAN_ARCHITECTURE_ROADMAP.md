# Clean Architecture Roadmap for Flutter Project

## 📋 **Executive Summary**

This document provides a comprehensive roadmap for implementing full Clean Architecture compliance in the Flutter project. Building upon the architectural improvements already completed in the previous 15 issues (AuthRepository, AuthValidationService, etc.), this plan uses the same conservative, surgical approach to achieve complete Clean Architecture implementation while maintaining existing functionality.

**Current Status**: Partial Clean Architecture implementation  
**Target**: Full Clean Architecture compliance with proper layer separation  
**Approach**: Conservative, surgical changes with backward compatibility  
**Timeline**: 4-phase implementation over 4 weeks  

## 🏗️ **Current Architecture Assessment**

### ✅ **Already Implemented (Foundation Built)**

**Repository Pattern**:
- `AuthRepository` interface with `MockAuthRepository` implementation
- Proper dependency injection in `AuthenticationBloc`
- Clear separation of authentication concerns

**Service Layer**:
- `AuthValidationService` for business logic extraction
- Validation logic separated from presentation layer
- Reusable validation methods

**Constants and Utilities**:
- `DefaultLandingData` for centralized feature/pricing data
- `DefaultUserData` for user-related constants
- `AppColors`, `AppDimensions` for design system
- `NavigationService` for routing abstraction

**BLoC Implementation**:
- Well-structured state management with proper event/state separation
- Comprehensive documentation and error handling
- Memory leak prevention and proper disposal

### ❌ **Current Project Structure**
```
/lib
  /core
    /repositories/          ⚠️ Should be in domain layer
      auth_repository.dart
    /services/              ⚠️ Business logic services
      auth_validation_service.dart
    /utils/                 ✅ Appropriate location
      app_colors.dart
      app_dimensions.dart
      default_landing_data.dart
      default_user_data.dart
      image_constant.dart
      navigator_service.dart
      size_utils.dart
  /presentation/            ✅ UI layer properly organized
    /authentication_screen/
    /landing_screen/
    /profile_screen/
    # ... other screens
  /widgets/                 ✅ Shared UI components
  /theme/                   ✅ Theme configuration
  /routes/                  ✅ Navigation setup
  /l10n/                    ✅ Localization
```

## 📊 **Clean Architecture Compliance Analysis**

### 1. **Layer Separation** - ⚠️ Partially Compliant

**Current Issues**:
- Repository interfaces in `/core` instead of `/domain`
- No dedicated domain layer for entities and use cases
- No dedicated data layer for implementations and data sources
- Business logic mixed in BLoCs instead of use cases

**Required Clean Architecture Layers**:
```
Domain Layer (Business Logic):
- Entities (business objects)
- Use Cases (business rules)
- Repository Interfaces (contracts)

Data Layer (External Concerns):
- Repository Implementations
- Data Sources (remote/local)
- DTOs and Mappers

Presentation Layer (UI):
- BLoCs (state management)
- Pages/Screens (UI)
- Widgets (UI components)
```

### 2. **Dependency Direction** - ❌ Not Enforced

**Current State**: Dependencies flow in multiple directions  
**Required**: Dependencies should flow inward (Presentation → Domain ← Data)

**Issues**:
- BLoCs directly depend on services instead of use cases
- Repository interfaces not in domain layer
- No clear dependency inversion enforcement

### 3. **BLoC Implementation** - ⚠️ Good but Improvable

**Strengths**:
- Proper event/state management
- Good error handling and documentation
- Repository pattern integration started

**Areas for Improvement**:
- Should use use cases instead of direct repository calls
- Business logic should be extracted to domain layer
- Better separation of concerns needed

### 4. **Missing Components**

**Domain Layer**:
- ❌ Domain entities (User, Feature, PricingPlan)
- ❌ Use cases (LoginUseCase, GetFeaturesUseCase)
- ❌ Repository interfaces in domain

**Data Layer**:
- ❌ Data sources (remote/local separation)
- ❌ DTOs separate from domain entities
- ❌ Repository implementations in data layer

**Infrastructure**:
- ❌ Dependency injection container
- ❌ Error handling framework
- ❌ Feature-based organization

## 🎯 **Conservative Implementation Plan**

### **Phase 1: Domain Layer Foundation** (Week 1)
**Priority**: Critical - Establishes business logic foundation

#### Step 1.1: Create Domain Entities
**Objective**: Define core business objects

**Implementation**:
```dart
// lib/domain/entities/user_entity.dart
class UserEntity {
  final String id;
  final String name;
  final String email;
  final String? profileImageUrl;

  const UserEntity({
    required this.id,
    required this.name,
    required this.email,
    this.profileImageUrl,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserEntity &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          name == other.name &&
          email == other.email &&
          profileImageUrl == other.profileImageUrl;

  @override
  int get hashCode => id.hashCode ^ name.hashCode ^ email.hashCode ^ profileImageUrl.hashCode;
}
```

```dart
// lib/domain/entities/feature_entity.dart
class FeatureEntity {
  final String id;
  final String title;
  final String description;
  final String iconPath;

  const FeatureEntity({
    required this.id,
    required this.title,
    required this.description,
    required this.iconPath,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is FeatureEntity &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          title == other.title &&
          description == other.description &&
          iconPath == other.iconPath;

  @override
  int get hashCode => id.hashCode ^ title.hashCode ^ description.hashCode ^ iconPath.hashCode;
}
```

```dart
// lib/domain/entities/pricing_plan_entity.dart
class PricingPlanEntity {
  final String id;
  final String price;
  final String title;
  final String description;
  final bool isPopular;
  final List<String> features;

  const PricingPlanEntity({
    required this.id,
    required this.price,
    required this.title,
    required this.description,
    required this.isPopular,
    required this.features,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PricingPlanEntity &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          price == other.price &&
          title == other.title &&
          description == other.description &&
          isPopular == other.isPopular &&
          const ListEquality().equals(features, other.features);

  @override
  int get hashCode =>
      id.hashCode ^
      price.hashCode ^
      title.hashCode ^
      description.hashCode ^
      isPopular.hashCode ^
      const ListEquality().hash(features);
}
```

#### Step 1.2: Create Domain Failures
**Objective**: Define domain-specific error types

```dart
// lib/domain/failures/auth_failure.dart
abstract class AuthFailure {
  final String message;
  const AuthFailure(this.message);
}

class ValidationFailure extends AuthFailure {
  final Map<String, String> errors;
  const ValidationFailure(this.errors) : super('Validation failed');
}

class AuthenticationFailure extends AuthFailure {
  const AuthenticationFailure() : super('Authentication failed');
}

class NetworkFailure extends AuthFailure {
  const NetworkFailure() : super('Network error occurred');
}

class UnknownFailure extends AuthFailure {
  const UnknownFailure(String message) : super(message);
}
```

```dart
// lib/domain/failures/landing_failure.dart
abstract class LandingFailure {
  final String message;
  const LandingFailure(this.message);
}

class DataLoadFailure extends LandingFailure {
  const DataLoadFailure() : super('Failed to load data');
}

class SubscriptionFailure extends LandingFailure {
  const SubscriptionFailure() : super('Subscription failed');
}
```

#### Step 1.3: Move Repository Interfaces to Domain
**Objective**: Establish domain contracts

```dart
// lib/domain/repositories/auth_repository.dart
import 'package:dartz/dartz.dart';
import '../entities/user_entity.dart';
import '../failures/auth_failure.dart';

abstract class AuthRepository {
  Future<Either<AuthFailure, UserEntity>> authenticateWithCredentials(
    String email,
    String password
  );
  Future<Either<AuthFailure, UserEntity>> authenticateWithGoogle();
  Future<Either<AuthFailure, UserEntity>> authenticateWithApple();
  Future<Either<AuthFailure, bool>> sendPasswordResetEmail(String email);
  Future<Either<AuthFailure, bool>> logout();
}
```

```dart
// lib/domain/repositories/landing_repository.dart
import 'package:dartz/dartz.dart';
import '../entities/feature_entity.dart';
import '../entities/pricing_plan_entity.dart';
import '../failures/landing_failure.dart';

abstract class LandingRepository {
  Future<Either<LandingFailure, List<FeatureEntity>>> getFeatures();
  Future<Either<LandingFailure, List<PricingPlanEntity>>> getPricingPlans();
  Future<Either<LandingFailure, bool>> subscribeToNewsletter(String email);
}
```

#### Step 1.4: Create Use Cases
**Objective**: Encapsulate business rules

```dart
// lib/domain/usecases/auth/login_usecase.dart
import 'package:dartz/dartz.dart';
import '../../entities/user_entity.dart';
import '../../failures/auth_failure.dart';
import '../../repositories/auth_repository.dart';
import '../../../core/services/auth_validation_service.dart';

class LoginParams {
  final String email;
  final String password;

  const LoginParams({required this.email, required this.password});
}

abstract class LoginUseCase {
  Future<Either<AuthFailure, UserEntity>> call(LoginParams params);
}

class LoginUseCaseImpl implements LoginUseCase {
  final AuthRepository _authRepository;

  LoginUseCaseImpl(this._authRepository);

  @override
  Future<Either<AuthFailure, UserEntity>> call(LoginParams params) async {
    // Validation
    final emailError = AuthValidationService.validateEmail(params.email);
    final passwordError = AuthValidationService.validatePassword(params.password);

    if (emailError != null || passwordError != null) {
      return Left(ValidationFailure({
        if (emailError != null) 'email': emailError,
        if (passwordError != null) 'password': passwordError,
      }));
    }

    // Authentication
    return await _authRepository.authenticateWithCredentials(
      params.email,
      params.password
    );
  }
}
```

```dart
// lib/domain/usecases/auth/google_login_usecase.dart
import 'package:dartz/dartz.dart';
import '../../entities/user_entity.dart';
import '../../failures/auth_failure.dart';
import '../../repositories/auth_repository.dart';

abstract class GoogleLoginUseCase {
  Future<Either<AuthFailure, UserEntity>> call();
}

class GoogleLoginUseCaseImpl implements GoogleLoginUseCase {
  final AuthRepository _authRepository;

  GoogleLoginUseCaseImpl(this._authRepository);

  @override
  Future<Either<AuthFailure, UserEntity>> call() async {
    return await _authRepository.authenticateWithGoogle();
  }
}
```

```dart
// lib/domain/usecases/landing/get_features_usecase.dart
import 'package:dartz/dartz.dart';
import '../../entities/feature_entity.dart';
import '../../failures/landing_failure.dart';
import '../../repositories/landing_repository.dart';

abstract class GetFeaturesUseCase {
  Future<Either<LandingFailure, List<FeatureEntity>>> call();
}

class GetFeaturesUseCaseImpl implements GetFeaturesUseCase {
  final LandingRepository _landingRepository;

  GetFeaturesUseCaseImpl(this._landingRepository);

  @override
  Future<Either<LandingFailure, List<FeatureEntity>>> call() async {
    return await _landingRepository.getFeatures();
  }
}
```

### **Phase 2: Data Layer Implementation** (Week 2)
**Priority**: High - Implements external concerns

#### Step 2.1: Create Data Sources
**Objective**: Separate remote and local data access

```dart
// lib/data/datasources/auth_remote_datasource.dart
import '../models/user_dto.dart';

abstract class AuthRemoteDataSource {
  Future<UserDto> login(String email, String password);
  Future<UserDto> loginWithGoogle();
  Future<UserDto> loginWithApple();
  Future<bool> sendPasswordResetEmail(String email);
  Future<bool> logout();
}

class AuthRemoteDataSourceImpl implements AuthRemoteDataSource {
  @override
  Future<UserDto> login(String email, String password) async {
    // Simulate API call with realistic delay
    await Future.delayed(Duration(milliseconds: 2700));

    // Simulate successful authentication
    return UserDto(
      id: 'user_${DateTime.now().millisecondsSinceEpoch}',
      name: 'John Doe',
      email: email,
      profileImageUrl: 'https://example.com/avatar.jpg',
    );
  }

  @override
  Future<UserDto> loginWithGoogle() async {
    await Future.delayed(Duration(milliseconds: 1500));
    return UserDto(
      id: 'google_user_${DateTime.now().millisecondsSinceEpoch}',
      name: 'Google User',
      email: '<EMAIL>',
      profileImageUrl: 'https://lh3.googleusercontent.com/avatar.jpg',
    );
  }

  @override
  Future<UserDto> loginWithApple() async {
    await Future.delayed(Duration(milliseconds: 1500));
    return UserDto(
      id: 'apple_user_${DateTime.now().millisecondsSinceEpoch}',
      name: 'Apple User',
      email: '<EMAIL>',
      profileImageUrl: null,
    );
  }

  @override
  Future<bool> sendPasswordResetEmail(String email) async {
    await Future.delayed(Duration(milliseconds: 1000));
    return true;
  }

  @override
  Future<bool> logout() async {
    await Future.delayed(Duration(milliseconds: 500));
    return true;
  }
}
```

```dart
// lib/data/datasources/auth_local_datasource.dart
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user_dto.dart';

abstract class AuthLocalDataSource {
  Future<void> cacheUser(UserDto user);
  Future<UserDto?> getCachedUser();
  Future<void> clearCache();
}

class AuthLocalDataSourceImpl implements AuthLocalDataSource {
  static const String _userKey = 'cached_user';

  @override
  Future<void> cacheUser(UserDto user) async {
    final prefs = await SharedPreferences.getInstance();
    final userJson = jsonEncode(user.toJson());
    await prefs.setString(_userKey, userJson);
  }

  @override
  Future<UserDto?> getCachedUser() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userJson = prefs.getString(_userKey);
      if (userJson != null) {
        final userMap = jsonDecode(userJson) as Map<String, dynamic>;
        return UserDto.fromJson(userMap);
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  @override
  Future<void> clearCache() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_userKey);
  }
}
```

#### Step 2.2: Create DTOs and Mappers
**Objective**: Handle data transformation between layers

```dart
// lib/data/models/user_dto.dart
import '../../domain/entities/user_entity.dart';

class UserDto {
  final String id;
  final String name;
  final String email;
  final String? profileImageUrl;

  UserDto({
    required this.id,
    required this.name,
    required this.email,
    this.profileImageUrl,
  });

  factory UserDto.fromJson(Map<String, dynamic> json) => UserDto(
    id: json['id'] as String,
    name: json['name'] as String,
    email: json['email'] as String,
    profileImageUrl: json['profile_image_url'] as String?,
  );

  Map<String, dynamic> toJson() => {
    'id': id,
    'name': name,
    'email': email,
    'profile_image_url': profileImageUrl,
  };

  UserEntity toEntity() => UserEntity(
    id: id,
    name: name,
    email: email,
    profileImageUrl: profileImageUrl,
  );

  factory UserDto.fromEntity(UserEntity entity) => UserDto(
    id: entity.id,
    name: entity.name,
    email: entity.email,
    profileImageUrl: entity.profileImageUrl,
  );
}
```

```dart
// lib/data/models/feature_dto.dart
import '../../domain/entities/feature_entity.dart';

class FeatureDto {
  final String id;
  final String title;
  final String description;
  final String iconPath;

  FeatureDto({
    required this.id,
    required this.title,
    required this.description,
    required this.iconPath,
  });

  factory FeatureDto.fromJson(Map<String, dynamic> json) => FeatureDto(
    id: json['id'] as String,
    title: json['title'] as String,
    description: json['description'] as String,
    iconPath: json['icon_path'] as String,
  );

  Map<String, dynamic> toJson() => {
    'id': id,
    'title': title,
    'description': description,
    'icon_path': iconPath,
  };

  FeatureEntity toEntity() => FeatureEntity(
    id: id,
    title: title,
    description: description,
    iconPath: iconPath,
  );
}
```

#### Step 2.3: Implement Repository Implementations
**Objective**: Connect domain contracts with data sources

```dart
// lib/data/repositories/auth_repository_impl.dart
import 'package:dartz/dartz.dart';
import '../../domain/entities/user_entity.dart';
import '../../domain/failures/auth_failure.dart';
import '../../domain/repositories/auth_repository.dart';
import '../datasources/auth_remote_datasource.dart';
import '../datasources/auth_local_datasource.dart';

class AuthRepositoryImpl implements AuthRepository {
  final AuthRemoteDataSource _remoteDataSource;
  final AuthLocalDataSource _localDataSource;

  AuthRepositoryImpl(this._remoteDataSource, this._localDataSource);

  @override
  Future<Either<AuthFailure, UserEntity>> authenticateWithCredentials(
    String email,
    String password
  ) async {
    try {
      final userDto = await _remoteDataSource.login(email, password);
      await _localDataSource.cacheUser(userDto);
      return Right(userDto.toEntity());
    } catch (e) {
      return Left(AuthenticationFailure());
    }
  }

  @override
  Future<Either<AuthFailure, UserEntity>> authenticateWithGoogle() async {
    try {
      final userDto = await _remoteDataSource.loginWithGoogle();
      await _localDataSource.cacheUser(userDto);
      return Right(userDto.toEntity());
    } catch (e) {
      return Left(AuthenticationFailure());
    }
  }

  @override
  Future<Either<AuthFailure, UserEntity>> authenticateWithApple() async {
    try {
      final userDto = await _remoteDataSource.loginWithApple();
      await _localDataSource.cacheUser(userDto);
      return Right(userDto.toEntity());
    } catch (e) {
      return Left(AuthenticationFailure());
    }
  }

  @override
  Future<Either<AuthFailure, bool>> sendPasswordResetEmail(String email) async {
    try {
      final result = await _remoteDataSource.sendPasswordResetEmail(email);
      return Right(result);
    } catch (e) {
      return Left(NetworkFailure());
    }
  }

  @override
  Future<Either<AuthFailure, bool>> logout() async {
    try {
      await _remoteDataSource.logout();
      await _localDataSource.clearCache();
      return Right(true);
    } catch (e) {
      return Left(UnknownFailure(e.toString()));
    }
  }
}
```

### **Phase 3: Dependency Injection Setup** (Week 3)
**Priority**: High - Enables proper dependency management

#### Step 3.1: Add Required Dependencies
**Objective**: Set up dependency injection framework

```yaml
# pubspec.yaml additions
dependencies:
  get_it: ^7.6.4
  injectable: ^2.3.2
  dartz: ^0.10.1

dev_dependencies:
  injectable_generator: ^2.4.1
  build_runner: ^2.4.7
```

#### Step 3.2: Create Service Locator
**Objective**: Central dependency registration

```dart
// lib/core/di/injection_container.dart
import 'package:get_it/get_it.dart';

// Domain
import '../../domain/usecases/auth/login_usecase.dart';
import '../../domain/usecases/auth/google_login_usecase.dart';
import '../../domain/usecases/auth/apple_login_usecase.dart';
import '../../domain/usecases/landing/get_features_usecase.dart';
import '../../domain/usecases/landing/get_pricing_plans_usecase.dart';
import '../../domain/repositories/auth_repository.dart';
import '../../domain/repositories/landing_repository.dart';

// Data
import '../../data/repositories/auth_repository_impl.dart';
import '../../data/repositories/landing_repository_impl.dart';
import '../../data/datasources/auth_remote_datasource.dart';
import '../../data/datasources/auth_local_datasource.dart';
import '../../data/datasources/landing_remote_datasource.dart';

// Presentation
import '../../presentation/authentication_screen/bloc/authentication_bloc.dart';
import '../../presentation/landing_screen/bloc/landing_bloc.dart';

// Core
import '../services/auth_validation_service.dart';

final sl = GetIt.instance;

Future<void> init() async {
  //! Features - Authentication
  // BLoCs
  sl.registerFactory(() => AuthenticationBloc(
    loginUseCase: sl(),
    googleLoginUseCase: sl(),
    appleLoginUseCase: sl(),
  ));

  // Use Cases
  sl.registerLazySingleton(() => LoginUseCaseImpl(sl()));
  sl.registerLazySingleton(() => GoogleLoginUseCaseImpl(sl()));
  sl.registerLazySingleton(() => AppleLoginUseCaseImpl(sl()));

  // Repository
  sl.registerLazySingleton<AuthRepository>(() => AuthRepositoryImpl(sl(), sl()));

  // Data Sources
  sl.registerLazySingleton<AuthRemoteDataSource>(() => AuthRemoteDataSourceImpl());
  sl.registerLazySingleton<AuthLocalDataSource>(() => AuthLocalDataSourceImpl());

  //! Features - Landing
  // BLoCs
  sl.registerFactory(() => LandingBloc(
    getFeaturesUseCase: sl(),
    getPricingPlansUseCase: sl(),
  ));

  // Use Cases
  sl.registerLazySingleton(() => GetFeaturesUseCaseImpl(sl()));
  sl.registerLazySingleton(() => GetPricingPlansUseCaseImpl(sl()));

  // Repository
  sl.registerLazySingleton<LandingRepository>(() => LandingRepositoryImpl(sl()));

  // Data Sources
  sl.registerLazySingleton<LandingRemoteDataSource>(() => LandingRemoteDataSourceImpl());

  //! Core
  sl.registerLazySingleton(() => AuthValidationService());
}
```

#### Step 3.3: Update Main App Initialization
**Objective**: Initialize dependency injection

```dart
// lib/main.dart updates
import 'core/di/injection_container.dart' as di;

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize dependency injection
  await di.init();

  // Rest of existing initialization...
  runApp(MyApp());
}
```

### **Phase 4: Update BLoCs to Use Use Cases** (Week 4)
**Priority**: Medium - Completes clean architecture implementation

#### Step 4.1: Refactor AuthenticationBloc
**Objective**: Use use cases instead of direct repository calls

```dart
// lib/presentation/authentication_screen/bloc/authentication_bloc.dart
import 'package:dartz/dartz.dart';
import '../../../domain/usecases/auth/login_usecase.dart';
import '../../../domain/usecases/auth/google_login_usecase.dart';
import '../../../domain/usecases/auth/apple_login_usecase.dart';
import '../../../domain/failures/auth_failure.dart';
import '../../../core/di/injection_container.dart';

class AuthenticationBloc extends Bloc<AuthenticationEvent, AuthenticationState> {
  final LoginUseCase _loginUseCase;
  final GoogleLoginUseCase _googleLoginUseCase;
  final AppleLoginUseCase _appleLoginUseCase;

  AuthenticationBloc({
    required LoginUseCase loginUseCase,
    required GoogleLoginUseCase googleLoginUseCase,
    required AppleLoginUseCase appleLoginUseCase,
  }) : _loginUseCase = loginUseCase,
       _googleLoginUseCase = googleLoginUseCase,
       _appleLoginUseCase = appleLoginUseCase,
       super(AuthenticationState.initial()) {
    on<AuthenticationInitialEvent>(_onInitialize);
    on<EmailChangedEvent>(_onEmailChanged);
    on<PasswordChangedEvent>(_onPasswordChanged);
    on<TogglePasswordVisibilityEvent>(_onTogglePasswordVisibility);
    on<LoginButtonTappedEvent>(_onLoginButtonTapped);
    on<GoogleLoginTappedEvent>(_onGoogleLoginTapped);
    on<AppleLoginTappedEvent>(_onAppleLoginTapped);
  }

  Future<void> _onLoginButtonTapped(
    LoginButtonTappedEvent event,
    Emitter<AuthenticationState> emit,
  ) async {
    // Start loading and animation
    emit(state.copyWith(isLoading: true, isAnimating: true));

    // Use case call
    final result = await _loginUseCase(LoginParams(
      email: state.emailController?.text ?? '',
      password: state.passwordController?.text ?? '',
    ));

    // Handle result
    result.fold(
      (failure) {
        String errorMessage = 'Login failed';
        Map<String, String> fieldErrors = {};

        if (failure is ValidationFailure) {
          fieldErrors = failure.errors;
          errorMessage = 'Please check your input';
        } else if (failure is AuthenticationFailure) {
          errorMessage = 'Invalid email or password';
        } else if (failure is NetworkFailure) {
          errorMessage = 'Network error. Please try again.';
        }

        emit(state.copyWith(
          isLoading: false,
          isAnimating: false,
          emailError: fieldErrors['email'],
          passwordError: fieldErrors['password'],
          generalError: fieldErrors.isEmpty ? errorMessage : null,
        ));
      },
      (user) {
        emit(state.copyWith(
          isLoading: false,
          isAnimating: false,
          isLoginSuccess: true,
          user: user,
        ));
      },
    );
  }

  Future<void> _onGoogleLoginTapped(
    GoogleLoginTappedEvent event,
    Emitter<AuthenticationState> emit,
  ) async {
    emit(state.copyWith(isLoading: true));

    final result = await _googleLoginUseCase();

    result.fold(
      (failure) => emit(state.copyWith(
        isLoading: false,
        generalError: 'Google login failed',
      )),
      (user) => emit(state.copyWith(
        isLoading: false,
        isLoginSuccess: true,
        user: user,
      )),
    );
  }

  Future<void> _onAppleLoginTapped(
    AppleLoginTappedEvent event,
    Emitter<AuthenticationState> emit,
  ) async {
    emit(state.copyWith(isLoading: true));

    final result = await _appleLoginUseCase();

    result.fold(
      (failure) => emit(state.copyWith(
        isLoading: false,
        generalError: 'Apple login failed',
      )),
      (user) => emit(state.copyWith(
        isLoading: false,
        isLoginSuccess: true,
        user: user,
      )),
    );
  }
}
```

#### Step 4.2: Update BLoC Instantiation
**Objective**: Use dependency injection for BLoC creation

```dart
// lib/presentation/authentication_screen/authentication_screen.dart
import '../../../core/di/injection_container.dart' as di;

class AuthenticationScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => di.sl<AuthenticationBloc>()
        ..add(AuthenticationInitialEvent()),
      child: BlocConsumer<AuthenticationBloc, AuthenticationState>(
        // ... rest of implementation
      ),
    );
  }
}
```

## 📁 **Recommended Final File Structure**

```
/lib
  /core
    /constants/                 # App-wide constants
      app_constants.dart
    /utils/                     # Utility functions
      app_colors.dart
      app_dimensions.dart
      image_constant.dart
      navigator_service.dart
      size_utils.dart
    /di/                        # Dependency injection
      injection_container.dart
    /errors/                    # Global error handling
      exceptions.dart
      error_handler.dart
    /services/                  # Cross-cutting services
      auth_validation_service.dart

  /domain
    /entities/                  # Business objects
      user_entity.dart
      feature_entity.dart
      pricing_plan_entity.dart
    /repositories/              # Repository interfaces (contracts)
      auth_repository.dart
      landing_repository.dart
    /usecases/                  # Business logic
      /auth/
        login_usecase.dart
        google_login_usecase.dart
        apple_login_usecase.dart
        logout_usecase.dart
      /landing/
        get_features_usecase.dart
        get_pricing_plans_usecase.dart
        subscribe_newsletter_usecase.dart
    /failures/                  # Domain-specific errors
      auth_failure.dart
      landing_failure.dart

  /data
    /models/                    # DTOs and data models
      user_dto.dart
      feature_dto.dart
      pricing_plan_dto.dart
    /repositories/              # Repository implementations
      auth_repository_impl.dart
      landing_repository_impl.dart
    /datasources/               # Data sources
      /remote/
        auth_remote_datasource.dart
        landing_remote_datasource.dart
      /local/
        auth_local_datasource.dart
        landing_local_datasource.dart

  /presentation
    /authentication/            # Feature-based organization
      /bloc/
        authentication_bloc.dart
        authentication_event.dart
        authentication_state.dart
      /pages/
        authentication_screen.dart
      /widgets/
        login_form_widget.dart
        social_login_buttons_widget.dart
    /landing/
      /bloc/
        landing_bloc.dart
        landing_event.dart
        landing_state.dart
      /pages/
        landing_screen.dart
      /widgets/
        hero_section_widget.dart
        features_section_widget.dart
        pricing_section_widget.dart
    /shared/                    # Shared UI components
      /widgets/
        custom_button.dart
        custom_input_field.dart
        custom_image_view.dart
        custom_appbar.dart
      /themes/
        theme_helper.dart
        text_style_helper.dart

  /routes                       # Navigation
    app_routes.dart

  /l10n                         # Localization
    app_localizations.dart
    app_en.arb
    app_ar.arb
```

## 🚀 **Implementation Strategy**

### **Conservative Migration Approach**

#### **Backward Compatibility Principles**
1. **Gradual Migration**: Implement new architecture alongside existing code
2. **Feature-by-Feature**: Start with authentication, then landing, etc.
3. **No Breaking Changes**: Existing functionality must continue working
4. **Incremental Testing**: Verify each step with `flutter analyze` and functionality tests

#### **Migration Steps**

**Week 1: Domain Layer Foundation**
- Day 1-2: Create domain entities and failures
- Day 3-4: Move repository interfaces to domain
- Day 5: Create use cases and test integration

**Week 2: Data Layer Implementation**
- Day 1-2: Create data sources and DTOs
- Day 3-4: Implement repository implementations
- Day 5: Test data layer integration

**Week 3: Dependency Injection Setup**
- Day 1-2: Add dependencies and create service locator
- Day 3-4: Register all dependencies
- Day 5: Update app initialization and test DI

**Week 4: BLoC Updates and Cleanup**
- Day 1-3: Update BLoCs to use use cases
- Day 4: Update BLoC instantiation to use DI
- Day 5: Clean up old code and final testing

#### **Testing Strategy**
1. **Unit Tests**: Test use cases and repository implementations
2. **Integration Tests**: Test complete feature flows
3. **Widget Tests**: Ensure UI continues working
4. **Manual Testing**: Verify all functionality works as expected

#### **Risk Mitigation**
1. **Feature Flags**: Use feature flags to toggle between old and new implementations
2. **Rollback Plan**: Keep old code until new implementation is fully verified
3. **Monitoring**: Monitor app performance and error rates during migration
4. **Staged Rollout**: Deploy to small user groups first

## 📈 **Benefits and Success Criteria**

### **Expected Benefits**

#### **Code Quality**
- ✅ **Maintainability**: Clear separation of concerns makes code easier to maintain
- ✅ **Testability**: Use cases and repositories are easily unit testable
- ✅ **Scalability**: Easy to add new features following the same pattern
- ✅ **Flexibility**: Easy to swap implementations (mock vs real, different APIs)

#### **Development Experience**
- ✅ **Team Productivity**: Clear structure helps team members understand and contribute
- ✅ **Code Reusability**: Use cases can be reused across different UI implementations
- ✅ **Debugging**: Clear data flow makes debugging easier
- ✅ **Documentation**: Well-structured code is self-documenting

#### **Technical Benefits**
- ✅ **Performance**: Better separation allows for optimized implementations
- ✅ **Error Handling**: Centralized error handling with proper failure types
- ✅ **Dependency Management**: Clear dependencies make code more modular
- ✅ **Future-Proofing**: Architecture supports future requirements and changes

### **Success Criteria**

#### **Technical Metrics**
1. **Code Coverage**: Achieve >80% test coverage for use cases and repositories
2. **Build Time**: Maintain or improve current build times
3. **App Performance**: No degradation in app startup time or runtime performance
4. **Code Quality**: Improve maintainability index and reduce cyclomatic complexity

#### **Functional Metrics**
1. **Feature Parity**: All existing features work exactly as before
2. **Error Handling**: Improved error messages and user feedback
3. **User Experience**: No changes to user-facing functionality
4. **Stability**: No increase in crash rates or error reports

#### **Development Metrics**
1. **Developer Velocity**: Faster feature development after initial setup
2. **Bug Resolution**: Faster bug identification and resolution
3. **Code Reviews**: Faster and more effective code reviews
4. **Onboarding**: Easier for new developers to understand and contribute

### **Measurement Plan**

#### **Before Migration**
- Baseline performance metrics (startup time, memory usage)
- Current test coverage and code quality metrics
- Developer productivity metrics (feature delivery time)

#### **During Migration**
- Weekly progress reviews against timeline
- Continuous integration and testing
- Performance monitoring and regression testing

#### **After Migration**
- Compare all metrics against baseline
- Developer satisfaction survey
- Code quality assessment
- Long-term maintenance cost analysis

## 🎯 **Next Steps**

1. **Review and Approve**: Review this roadmap with the development team
2. **Environment Setup**: Set up development environment with new dependencies
3. **Phase 1 Kickoff**: Begin with domain layer foundation
4. **Regular Reviews**: Weekly progress reviews and adjustments
5. **Documentation Updates**: Keep documentation updated throughout migration

---

**This roadmap builds upon the successful architectural improvements already completed in the previous 15 issues, using the same conservative, surgical approach to achieve full Clean Architecture compliance while maintaining existing functionality and development velocity.**
```
```
```
