import 'package:bloc_test/bloc_test.dart';
import 'package:dartz/dartz.dart';
import 'package:flutter/material.dart';
import 'package:flutter_project/domain/entities/user_entity.dart';
import 'package:flutter_project/domain/failures/auth_failure.dart';
import 'package:flutter_project/domain/usecases/auth/apple_login_usecase.dart';
import 'package:flutter_project/domain/usecases/auth/google_login_usecase.dart';
import 'package:flutter_project/domain/usecases/auth/login_usecase.dart';
import 'package:flutter_project/presentation/authentication_screen/bloc/authentication_bloc.dart';
import 'package:flutter_project/presentation/authentication_screen/models/authentication_model.dart';
import 'package:flutter_test/flutter_test.dart';

// Mock use cases for testing
class MockLoginUseCase implements LoginUseCase {
  @override
  Future<Either<AuthFailure, UserEntity>> call(LoginParams params) async {
    return Right(UserEntity(id: '1', name: 'Test User', email: params.email));
  }
}

class MockGoogleLoginUseCase implements GoogleLoginUseCase {
  @override
  Future<Either<AuthFailure, UserEntity>> call() async {
    return Right(UserEntity(id: '1', name: 'Google User', email: '<EMAIL>'));
  }
}

class MockAppleLoginUseCase implements AppleLoginUseCase {
  @override
  Future<Either<AuthFailure, UserEntity>> call() async {
    return Right(UserEntity(id: '1', name: 'Apple User', email: '<EMAIL>'));
  }
}

void main() {
  group('Debug Error Clearing Tests', () {
    late AuthenticationBloc authenticationBloc;
    late MockLoginUseCase mockLoginUseCase;
    late MockGoogleLoginUseCase mockGoogleLoginUseCase;
    late MockAppleLoginUseCase mockAppleLoginUseCase;

    setUp(() {
      mockLoginUseCase = MockLoginUseCase();
      mockGoogleLoginUseCase = MockGoogleLoginUseCase();
      mockAppleLoginUseCase = MockAppleLoginUseCase();

      authenticationBloc = AuthenticationBloc(
        AuthenticationState(authenticationModel: AuthenticationModel()),
        loginUseCase: mockLoginUseCase,
        googleLoginUseCase: mockGoogleLoginUseCase,
        appleLoginUseCase: mockAppleLoginUseCase,
      );
    });

    tearDown(() {
      authenticationBloc.close();
    });

    test('copyWith method should clear errors when flags are true', () {
      // Create a state with errors
      final stateWithErrors = AuthenticationState(
        emailError: 'Email is required',
        passwordError: 'Password is required',
        authenticationModel: AuthenticationModel(),
      );

      // Use copyWith to clear errors
      final clearedState = stateWithErrors.copyWith(clearEmailError: true, clearPasswordError: true);

      // Verify errors are cleared
      expect(clearedState.emailError, isNull);
      expect(clearedState.passwordError, isNull);
    });

    test('copyWith method should preserve errors when flags are false', () {
      // Create a state with errors
      final stateWithErrors = AuthenticationState(
        emailError: 'Email is required',
        passwordError: 'Password is required',
        authenticationModel: AuthenticationModel(),
      );

      // Use copyWith without clearing flags
      final preservedState = stateWithErrors.copyWith(isLoading: true);

      // Verify errors are preserved
      expect(preservedState.emailError, equals('Email is required'));
      expect(preservedState.passwordError, equals('Password is required'));
    });

    blocTest<AuthenticationBloc, AuthenticationState>(
      'should clear errors when login with valid credentials',
      build: () => authenticationBloc,
      seed: () => AuthenticationState(
        emailController: TextEditingController()..text = '<EMAIL>',
        passwordController: TextEditingController()..text = 'password123',
        emailError: 'Previous email error',
        passwordError: 'Previous password error',
        authenticationModel: AuthenticationModel(),
      ),
      act: (bloc) => bloc.add(LoginButtonTappedEvent()),
      expect: () => [
        // First emission should clear errors and start loading
        isA<AuthenticationState>()
            .having((state) => state.isLoading, 'isLoading', true)
            .having((state) => state.emailError, 'emailError', null)
            .having((state) => state.passwordError, 'passwordError', null),
      ],
      wait: Duration(seconds: 1), // Only wait for the first emission
    );

    blocTest<AuthenticationBloc, AuthenticationState>(
      'should show errors when login with invalid credentials',
      build: () => authenticationBloc,
      seed: () => AuthenticationState(
        emailController: TextEditingController()..text = 'invalid-email',
        passwordController: TextEditingController()..text = '123',
        authenticationModel: AuthenticationModel(),
      ),
      act: (bloc) => bloc.add(LoginButtonTappedEvent()),
      expect: () => [
        isA<AuthenticationState>()
            .having((state) => state.emailError, 'emailError', 'Please enter a valid email address')
            .having((state) => state.passwordError, 'passwordError', 'Password must be at least 6 characters'),
      ],
    );
  });
}
