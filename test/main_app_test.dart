import 'package:flutter/material.dart';
import 'package:flutter_project/core/di/injection_container.dart' as di;
import 'package:flutter_project/main.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';

void main() {
  group('Main App Theme Tests', () {
    setUp(() async {
      SharedPreferences.setMockInitialValues({});
      // Initialize dependency injection for tests
      await di.init();
    });

    tearDown(() async {
      // Reset dependency injection after each test
      await di.reset();
    });

    testWidgets('App should start with light theme when no preference is set', (WidgetTester tester) async {
      final prefs = await SharedPreferences.getInstance();

      await tester.pumpWidget(MyApp(prefs: prefs));
      await tester.pumpAndSettle();

      // Find the MaterialApp widget
      final materialApp = tester.widget<MaterialApp>(find.byType(MaterialApp));

      // Verify that themeMode is set to light (since isDarkMode defaults to false)
      expect(materialApp.themeMode, ThemeMode.light);
    });

    testWidgets('App should start with dark theme when dark mode preference is set', (WidgetTester tester) async {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('isDarkMode', true);

      await tester.pumpWidget(MyApp(prefs: prefs));
      await tester.pumpAndSettle();

      // Find the MaterialApp widget
      final materialApp = tester.widget<MaterialApp>(find.byType(MaterialApp));

      // Verify that themeMode is set to dark (since isDarkMode is true)
      expect(materialApp.themeMode, ThemeMode.dark);
    });

    testWidgets('App should start with light theme when light mode preference is set', (WidgetTester tester) async {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('isDarkMode', false);

      await tester.pumpWidget(MyApp(prefs: prefs));
      await tester.pumpAndSettle();

      // Find the MaterialApp widget
      final materialApp = tester.widget<MaterialApp>(find.byType(MaterialApp));

      // Verify that themeMode is set to light (since isDarkMode is false)
      expect(materialApp.themeMode, ThemeMode.light);
    });

    testWidgets('App should have both light and dark themes defined', (WidgetTester tester) async {
      final prefs = await SharedPreferences.getInstance();

      await tester.pumpWidget(MyApp(prefs: prefs));
      await tester.pumpAndSettle();

      // Find the MaterialApp widget
      final materialApp = tester.widget<MaterialApp>(find.byType(MaterialApp));

      // Verify that both themes are defined
      expect(materialApp.theme, isNotNull);
      expect(materialApp.darkTheme, isNotNull);

      // Verify that the themes have the correct brightness
      expect(materialApp.theme!.brightness, Brightness.light);
      expect(materialApp.darkTheme!.brightness, Brightness.dark);
    });
  });
}
