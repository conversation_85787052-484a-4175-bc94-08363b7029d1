import 'package:flutter/material.dart';
import 'package:flutter_project/main.dart';
import 'package:flutter_project/theme/theme_helper.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';

void main() {
  group('Comprehensive Theme Tests', () {
    setUp(() {
      SharedPreferences.setMockInitialValues({});
    });

    testWidgets('Theme helper should update colors when theme mode changes', (WidgetTester tester) async {
      // Test light theme colors
      themeHelperInstance.updateThemeMode(false);
      expect(appTheme.whiteCustom, equals(Colors.white));
      expect(appTheme.blackCustom, equals(Colors.black));
      expect(appTheme.colorFF1616, equals(Color(0xFF161616)));

      // Test dark theme colors
      themeHelperInstance.updateThemeMode(true);
      expect(appTheme.whiteCustom, equals(Color(0xFF1E1E1E)));
      expect(appTheme.blackCustom, equals(Colors.white));
      expect(appTheme.colorFF1616, equals(Color(0xFFE5E5E5)));
    });

    testWidgets('App should apply correct theme colors when theme changes', (WidgetTester tester) async {
      final prefs = await SharedPreferences.getInstance();

      await tester.pumpWidget(MyApp(prefs: prefs));
      await tester.pumpAndSettle();

      // Find the MaterialApp widget
      final materialApp = tester.widget<MaterialApp>(find.byType(MaterialApp));

      // Verify light theme is applied initially
      expect(materialApp.themeMode, ThemeMode.light);
      expect(materialApp.theme!.brightness, Brightness.light);
      expect(materialApp.darkTheme!.brightness, Brightness.dark);

      // Verify theme colors are properly defined
      expect(materialApp.theme!.colorScheme.primary, Color(0xFF7E56D8));
      expect(materialApp.theme!.colorScheme.surface, Color(0xFFFFFFFF));
      expect(materialApp.darkTheme!.colorScheme.surface, Color(0xFF1E1E1E));
    });

    testWidgets('Theme data should include TabBar theme', (WidgetTester tester) async {
      final prefs = await SharedPreferences.getInstance();

      await tester.pumpWidget(MyApp(prefs: prefs));
      await tester.pumpAndSettle();

      // Find the MaterialApp widget
      final materialApp = tester.widget<MaterialApp>(find.byType(MaterialApp));

      // Verify TabBar theme is properly configured
      expect(materialApp.theme!.tabBarTheme.labelColor, isNotNull);
      expect(materialApp.theme!.tabBarTheme.unselectedLabelColor, isNotNull);
      expect(materialApp.theme!.tabBarTheme.dividerColor, Colors.transparent);
      expect(materialApp.darkTheme!.tabBarTheme.labelColor, isNotNull);
      expect(materialApp.darkTheme!.tabBarTheme.unselectedLabelColor, isNotNull);
    });

    testWidgets('Custom widgets should adapt to theme changes', (WidgetTester tester) async {
      final prefs = await SharedPreferences.getInstance();

      await tester.pumpWidget(MyApp(prefs: prefs));
      await tester.pumpAndSettle();

      // Check that scaffold background uses theme color
      final scaffold = tester.widget<Scaffold>(find.byType(Scaffold).first);
      expect(scaffold.backgroundColor, isNotNull);
    });

    test('Theme bloc should update theme helper', () {
      // Test that theme helper updates when bloc state changes
      themeHelperInstance.updateThemeMode(false);
      expect(appTheme.whiteCustom, equals(Colors.white));

      themeHelperInstance.updateThemeMode(true);
      expect(appTheme.whiteCustom, equals(Color(0xFF1E1E1E)));

      // Reset to light mode
      themeHelperInstance.updateThemeMode(false);
    });

    test('ColorSchemes should have proper contrast ratios', () {
      final lightScheme = ColorSchemes.lightCodeColorScheme;
      final darkScheme = ColorSchemes.darkCodeColorScheme;

      // Verify light theme has dark text on light background
      expect(lightScheme.surface.computeLuminance() > lightScheme.onSurface.computeLuminance(), true);

      // Verify dark theme has light text on dark background
      expect(darkScheme.surface.computeLuminance() < darkScheme.onSurface.computeLuminance(), true);

      // Verify primary colors are consistent
      expect(lightScheme.primary, darkScheme.primary);
    });

    test('DarkCodeColors should override all necessary colors', () {
      final darkColors = DarkCodeColors();

      // Verify key colors are overridden for dark theme
      expect(darkColors.whiteCustom, isNot(equals(Colors.white)));
      expect(darkColors.blackCustom, isNot(equals(Colors.black)));
      expect(darkColors.colorFF1616, isNot(equals(Color(0xFF161616))));
      expect(darkColors.colorFFF8F9FB, isNot(equals(Color(0xFFF8F9FB))));
    });
  });
}
