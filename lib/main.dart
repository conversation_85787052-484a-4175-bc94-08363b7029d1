import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'core/app_export.dart';
import 'core/di/injection_container.dart' as di;
import 'l10n/app_localizations.dart';
import 'presentation/localization/bloc/localization_bloc.dart';
import 'presentation/theme/bloc/theme_bloc.dart';

var globalMessengerKey = GlobalKey<ScaffoldMessengerState>();

void main() async {
  try {
    WidgetsFlutterBinding.ensureInitialized();

    // Initialize dependency injection
    await di.init();

    final prefs = await SharedPreferences.getInstance();

    await Future.wait([
      SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]),
    ]);

    runApp(MyApp(prefs: prefs));
  } catch (e) {
    // Handle initialization errors gracefully
    runApp(_ErrorApp(error: e.toString()));
  }
}

/// Simple error app to display when initialization fails
class _ErrorApp extends StatelessWidget {
  final String error;

  const _ErrorApp({required this.error});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Initialization Error',
      home: Scaffold(
        body: Center(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error_outline, size: 64, color: Colors.red),
                const SizedBox(height: 16),
                const Text(
                  'App Initialization Failed',
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                Text('Error: $error', style: const TextStyle(fontSize: 14), textAlign: TextAlign.center),
                const SizedBox(height: 16),
                const Text(
                  'Please restart the app or contact support if the problem persists.',
                  style: TextStyle(fontSize: 12),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class MyApp extends StatelessWidget {
  final SharedPreferences prefs;

  const MyApp({super.key, required this.prefs});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<ThemeBloc>(create: (context) => ThemeBloc(prefs)..add(LoadThemeEvent())),
        BlocProvider<LocalizationBloc>(create: (context) => LocalizationBloc(prefs)..add(LoadLanguageEvent())),
      ],
      child: BlocBuilder<ThemeBloc, ThemeState>(
        builder: (context, themeState) {
          return BlocBuilder<LocalizationBloc, LocalizationState>(
            builder: (context, localeState) {
              // Update the theme helper with current theme state
              // This ensures theme is updated even after language changes
              themeHelperInstance.updateThemeMode(themeState.isDarkMode);
              return Sizer(
                builder: (context, orientation, deviceType) {
                  return MaterialApp(
                    title: 'authentication_app',
                    themeMode: themeState.isDarkMode ? ThemeMode.dark : ThemeMode.light,
                    theme: lightTheme,
                    darkTheme: darkTheme,
                    builder: (context, child) {
                      return Directionality(
                        textDirection: localeState.locale.languageCode == 'ar' ? TextDirection.rtl : TextDirection.ltr,
                        child: MediaQuery(
                          data: MediaQuery.of(context).copyWith(textScaler: TextScaler.linear(1.0)),
                          child: child!,
                        ),
                      );
                    },
                    navigatorKey: NavigatorService.navigatorKey,
                    debugShowCheckedModeBanner: false,
                    localizationsDelegates: [
                      AppLocalizations.delegate,
                      GlobalMaterialLocalizations.delegate,
                      GlobalWidgetsLocalizations.delegate,
                      GlobalCupertinoLocalizations.delegate,
                    ],

                    supportedLocales: [Locale('en', ''), Locale('ar', '')],
                    locale: localeState.locale,
                    initialRoute: AppRoutes.initialRoute,
                    routes: AppRoutes.routes,
                  );
                },
              );
            },
          );
        },
      ),
    );
  }
}
