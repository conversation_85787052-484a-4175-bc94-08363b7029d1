import '../models/feature_dto.dart';
import '../models/pricing_plan_dto.dart';
import '../../core/utils/image_constant.dart';

/// Remote data source interface for landing page operations.
/// 
/// This interface defines the contract for remote landing page data operations
/// following Clean Architecture principles. It abstracts the details of external
/// APIs and services from the repository layer.
/// 
/// The data source is responsible for handling network communication,
/// API calls, and data serialization/deserialization for landing page content.
abstract class LandingRemoteDataSource {
  /// Retrieves app features from remote API.
  /// 
  /// Returns [List<FeatureDto>] on successful data retrieval.
  /// Throws exceptions on failure (network, server errors).
  Future<List<FeatureDto>> getFeatures();

  /// Retrieves pricing plans from remote API.
  /// 
  /// Returns [List<PricingPlanDto>] on successful data retrieval.
  /// Throws exceptions on failure (network, server errors).
  Future<List<PricingPlanDto>> getPricingPlans();

  /// Subscribes email to newsletter via remote API.
  /// 
  /// Returns [true] if subscription was successful.
  /// Throws exceptions on failure (network, server, validation errors).
  Future<bool> subscribeToNewsletter(String email);
}

/// Implementation of the remote landing page data source.
/// 
/// This class handles actual network communication with landing page APIs.
/// It simulates real API calls with realistic delays and responses for
/// development and testing purposes.
/// 
/// In a production environment, this would make actual HTTP requests
/// to content management systems or marketing APIs using packages like dio or http.
class LandingRemoteDataSourceImpl implements LandingRemoteDataSource {
  @override
  Future<List<FeatureDto>> getFeatures() async {
    // Simulate API call with realistic delay
    await Future.delayed(Duration(milliseconds: 800));
    
    // Simulate API response with feature data
    // In production, this would parse actual API response
    return [
      FeatureDto(
        id: 'feature_1',
        title: 'Access to daily analytics',
        description: 'Optimize the way you recover, train, and sleep with daily reporting on mobile and desktop apps. Start training smarter, not harder.',
        iconPath: ImageConstant.imgPiechart,
      ),
      FeatureDto(
        id: 'feature_2',
        title: 'Measure recovery',
        description: 'The most advanced sleep tracking technology available today. Measure and track your recovery to unlock your greatest potential.',
        iconPath: ImageConstant.imgZap,
      ),
      FeatureDto(
        id: 'feature_3',
        title: 'Tech that evolves with you',
        description: 'Know where your strengths lie and where you can improve. Untitled provides the latest tech with a steady stream of new features.',
        iconPath: ImageConstant.imgSmartphone,
      ),
      FeatureDto(
        id: 'feature_4',
        title: 'Unrivalled community',
        description: 'Join teams in the app with friends, family, and like-minded fitness enthusiasts. Create custom teams based on activities and interests.',
        iconPath: ImageConstant.imgUsers,
      ),
    ];
  }

  @override
  Future<List<PricingPlanDto>> getPricingPlans() async {
    // Simulate API call with realistic delay
    await Future.delayed(Duration(milliseconds: 600));
    
    // Simulate API response with pricing data
    // In production, this would parse actual API response
    return [
      PricingPlanDto(
        id: 'plan_basic',
        price: '\$10/mth',
        title: 'Basic plan',
        description: 'Billed annually.',
        isPopular: true,
        features: [
          'Access to all basic features',
          'Basic reporting and analytics',
          'Up to 10 individual users',
          '20GB individual data each user',
          'Basic chat and email support',
        ],
      ),
      PricingPlanDto(
        id: 'plan_business',
        price: '\$20/mth',
        title: 'Business plan',
        description: 'Billed annually.',
        isPopular: false,
        features: [
          '200+ integrations',
          'Advanced reporting',
          'Up to 20 individual users',
          '40GB individual data each user',
          'Priority chat and email support',
        ],
      ),
    ];
  }

  @override
  Future<bool> subscribeToNewsletter(String email) async {
    // Simulate newsletter subscription API call
    await Future.delayed(Duration(milliseconds: 1200));
    
    // Simulate validation and subscription logic
    // In production, this would validate email and submit to newsletter service
    if (email.isEmpty || !email.contains('@')) {
      throw Exception('Invalid email format');
    }
    
    // Simulate successful subscription
    return true;
  }
}
