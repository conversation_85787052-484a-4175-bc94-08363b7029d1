import 'package:get_it/get_it.dart';

import '../../data/datasources/auth_local_datasource.dart';
// Data - Data Sources
import '../../data/datasources/auth_remote_datasource.dart';
import '../../data/datasources/landing_remote_datasource.dart';
// Data - Repositories
import '../../data/repositories/auth_repository_impl.dart';
import '../../data/repositories/landing_repository_impl.dart';
// Domain - Repositories
import '../../domain/repositories/auth_repository.dart';
import '../../domain/repositories/landing_repository.dart';
import '../../domain/usecases/auth/apple_login_usecase.dart';
import '../../domain/usecases/auth/google_login_usecase.dart';
// Domain - Use Cases
import '../../domain/usecases/auth/login_usecase.dart';
import '../../domain/usecases/landing/get_features_usecase.dart';
import '../../domain/usecases/landing/get_pricing_plans_usecase.dart';
// Presentation - BLoCs
import '../../presentation/authentication_screen/bloc/authentication_bloc.dart';
// Presentation - Models
import '../../presentation/authentication_screen/models/authentication_model.dart';
import '../../presentation/landing_screen/bloc/landing_bloc.dart';
import '../../presentation/landing_screen/models/landing_model.dart';

/// Service locator instance for dependency injection.
///
/// This is the global instance of GetIt that manages all dependencies
/// throughout the application. It follows the Service Locator pattern
/// to provide centralized dependency management.
final sl = GetIt.instance;

/// Initializes all dependencies for the application.
///
/// This function sets up the dependency injection container by registering
/// all required dependencies in the correct order. It follows Clean Architecture
/// principles by registering dependencies from the innermost layer (domain)
/// to the outermost layer (presentation).
///
/// Registration order:
/// 1. Core services and utilities
/// 2. Data sources (external dependencies)
/// 3. Repositories (domain contracts with data implementations)
/// 4. Use cases (business logic)
/// 5. BLoCs (presentation layer)
///
/// This function should be called once during app initialization,
/// typically in the main() function before runApp().
Future<void> init() async {
  //! Core Services
  // AuthValidationService is a static utility class, no need to register

  //! Data Sources
  // Register remote data sources as singletons
  sl.registerLazySingleton<AuthRemoteDataSource>(() => AuthRemoteDataSourceImpl());

  sl.registerLazySingleton<AuthLocalDataSource>(() => AuthLocalDataSourceImpl());

  sl.registerLazySingleton<LandingRemoteDataSource>(() => LandingRemoteDataSourceImpl());

  //! Repositories
  // Register repository implementations as singletons
  // They implement domain interfaces but use data layer implementations
  sl.registerLazySingleton<AuthRepository>(() => AuthRepositoryImpl(sl(), sl()));

  sl.registerLazySingleton<LandingRepository>(() => LandingRepositoryImpl(sl()));

  //! Use Cases
  // Register use cases as singletons since they're stateless business logic
  sl.registerLazySingleton(() => LoginUseCaseImpl(sl()));
  sl.registerLazySingleton(() => GoogleLoginUseCaseImpl(sl()));
  sl.registerLazySingleton(() => AppleLoginUseCaseImpl(sl()));
  sl.registerLazySingleton(() => GetFeaturesUseCaseImpl(sl()));
  sl.registerLazySingleton(() => GetPricingPlansUseCaseImpl(sl()));

  //! BLoCs
  // Register BLoCs as factories since they maintain state and should be
  // created fresh for each screen/widget that needs them
  // Note: These will be updated in Phase 4 to use use cases
  sl.registerFactory(
    () => AuthenticationBloc(
      AuthenticationState(authenticationModel: AuthenticationModel()),
      loginUseCase: sl(),
      googleLoginUseCase: sl(),
      appleLoginUseCase: sl(),
    ),
  );

  sl.registerFactory(
    () => LandingBloc(
      LandingState(landingModel: LandingModel()),
      getFeaturesUseCase: sl(),
      getPricingPlansUseCase: sl(),
    ),
  );
}

/// Resets all dependencies in the service locator.
///
/// This function is primarily used for testing to ensure a clean state
/// between test runs. It clears all registered dependencies and allows
/// for fresh registration in test environments.
///
/// Should not be used in production code.
Future<void> reset() async {
  await sl.reset();
}
