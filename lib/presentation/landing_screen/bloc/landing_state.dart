part of 'landing_bloc.dart';

class LandingState extends Equatable {
  final LandingModel? landingModel;
  final TextEditingController? emailController;
  final String? email;
  final bool isSubscribed;
  final String? selectedNavItem;
  final PricingPlanModel? selectedPlan;

  const LandingState({
    this.landingModel,
    this.emailController,
    this.email,
    this.isSubscribed = false,
    this.selectedNavItem,
    this.selectedPlan,
  });

  @override
  List<Object?> get props => [
        landingModel,
        emailController,
        email,
        isSubscribed,
        selectedNavItem,
        selectedPlan,
      ];

  LandingState copyWith({
    LandingModel? landingModel,
    TextEditingController? emailController,
    String? email,
    bool? isSubscribed,
    String? selectedNavItem,
    PricingPlanModel? selectedPlan,
  }) {
    return LandingState(
      landingModel: landingModel ?? this.landingModel,
      emailController: emailController ?? this.emailController,
      email: email ?? this.email,
      isSubscribed: isSubscribed ?? this.isSubscribed,
      selectedNavItem: selectedNavItem ?? this.selectedNavItem,
      selectedPlan: selectedPlan ?? this.selectedPlan,
    );
  }
}
