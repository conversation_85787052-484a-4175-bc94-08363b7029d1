import 'package:flutter/material.dart';

import '../../../core/app_export.dart';
import '../../../widgets/custom_image_view.dart';

class PhoneMockupWidget extends StatelessWidget {
  final String? backgroundImage;
  final String screenImage;
  final String? phoneFrameImage;
  final String signalIcon;
  final String batteryIcon;
  final double height;
  final bool showComplexFrame;

  const PhoneMockupWidget({
    super.key,
    this.backgroundImage,
    required this.screenImage,
    this.phoneFrameImage,
    required this.signalIcon,
    required this.batteryIcon,
    required this.height,
    this.showComplexFrame = false,
  });

  @override
  Widget build(BuildContext context) {
    Widget phoneContent = Container(
      margin: EdgeInsets.symmetric(horizontal: 16.h),
      padding: EdgeInsets.all(16.h),
      decoration: BoxDecoration(color: appTheme.whiteCustom, borderRadius: BorderRadius.circular(8.h)),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              CustomImageView(imagePath: signalIcon, height: 8.h, width: 16.h),
              CustomImageView(imagePath: batteryIcon, height: 8.h, width: 24.h),
            ],
          ),
          SizedBox(height: 8.h),
          CustomImageView(imagePath: screenImage, height: height, width: double.maxFinite, fit: BoxFit.cover),
        ],
      ),
    );

    if (backgroundImage != null) {
      phoneContent = Container(
        height: 380.h,
        width: double.maxFinite,
        decoration: BoxDecoration(
          image: DecorationImage(image: AssetImage(backgroundImage!), fit: BoxFit.cover),
        ),
        child: Padding(
          padding: EdgeInsets.only(top: 64.h),
          child: phoneContent,
        ),
      );
    }

    if (showComplexFrame) {
      phoneContent = Stack(
        children: [
          phoneContent,
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Center(child: CustomImageView(imagePath: ImageConstant.imgButtons)),
          ),
          Positioned.fill(
            child: CustomImageView(imagePath: ImageConstant.imgDeviceSurround, fit: BoxFit.contain),
          ),
          Positioned.fill(
            child: CustomImageView(imagePath: ImageConstant.imgHighlightBand, fit: BoxFit.contain),
          ),
          Positioned.fill(
            child: CustomImageView(imagePath: ImageConstant.imgBackground, fit: BoxFit.contain),
          ),
          Positioned.fill(
            child: CustomImageView(imagePath: ImageConstant.imgAntennaBands, fit: BoxFit.contain),
          ),
          Positioned(
            top: 16.h,
            left: 0,
            right: 0,
            child: Center(
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CustomImageView(imagePath: ImageConstant.imgSpeaker, height: 4.h, width: 32.h),
                  SizedBox(width: 32.h),
                  CustomImageView(imagePath: ImageConstant.imgCamera, height: 12.h, width: 12.h),
                ],
              ),
            ),
          ),
        ],
      );
    } else if (phoneFrameImage != null) {
      phoneContent = Stack(
        children: [
          phoneContent,
          Positioned.fill(
            child: CustomImageView(imagePath: phoneFrameImage!, fit: BoxFit.contain),
          ),
        ],
      );
    }

    return phoneContent;
  }
}
