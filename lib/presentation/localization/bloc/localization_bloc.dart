import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../core/app_export.dart';

part 'localization_event.dart';
part 'localization_state.dart';

/// Manages application localization and language preferences.
///
/// This BLoC handles language switching, persisting the user's language
/// preference to SharedPreferences, and loading the saved language on
/// app startup. It supports multiple locales and ensures language
/// consistency across app restarts.
///
/// Key responsibilities:
/// - Language state management and locale switching
/// - Language preference persistence via SharedPreferences
/// - Language loading on app initialization
/// - Error handling for storage failures
class LocalizationBloc extends Bloc<LocalizationEvent, LocalizationState> {
  final SharedPreferences prefs;
  static const String _languageKey = 'selectedLanguage';

  LocalizationBloc(this.prefs) : super(const LocalizationState()) {
    on<LoadLanguageEvent>(_onLoadLanguage);
    on<ChangeLanguageEvent>(_onChangeLanguage);
  }

  Future<void> _onLoadLanguage(LoadLanguageEvent event, Emitter<LocalizationState> emit) async {
    final languageCode = prefs.getString(_languageKey) ?? 'en';
    emit(state.copyWith(locale: Locale(languageCode)));
  }

  Future<void> _onChangeLanguage(ChangeLanguageEvent event, Emitter<LocalizationState> emit) async {
    try {
      await prefs.setString(_languageKey, event.locale.languageCode);
      emit(state.copyWith(locale: event.locale));
    } catch (e) {
      debugPrint('Failed to save language preference: $e');
      // Keep current state - don't update UI if save failed
    }
  }
}
