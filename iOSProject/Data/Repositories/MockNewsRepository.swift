import Foundation

// MARK: - Mock News Repository
class MockNewsRepository: NewsRepositoryProtocol {
    
    // Mock news data
    private let mockNews: [NewsItem] = [
        NewsItem(
            id: "news_1",
            title: "Breaking: New iOS Features Announced",
            description: "Apple announces exciting new features for iOS developers",
            content: "Apple has announced several new features for iOS developers including enhanced SwiftUI capabilities...",
            imageURL: "https://example.com/news1.jpg",
            author: "Tech Reporter",
            publishedAt: Date().addingTimeInterval(-86400), // 1 day ago
            category: .technology
        ),
        NewsItem(
            id: "news_2",
            title: "SwiftUI Best Practices for 2024",
            description: "Learn the latest best practices for SwiftUI development",
            content: "SwiftUI continues to evolve with new patterns and best practices emerging...",
            imageURL: "https://example.com/news2.jpg",
            author: "iOS Developer",
            publishedAt: Date().addingTimeInterval(-172800), // 2 days ago
            category: .technology
        ),
        NewsItem(
            id: "news_3",
            title: "App Store Guidelines Updated",
            description: "Important updates to App Store review guidelines",
            content: "Apple has updated its App Store review guidelines with new requirements...",
            imageURL: "https://example.com/news3.jpg",
            author: "App Store Team",
            publishedAt: Date().addingTimeInterval(-259200), // 3 days ago
            category: .business
        )
    ]
    
    func getNews() async throws -> [NewsItem] {
        // Simulate network delay
        try await Task.sleep(nanoseconds: 1_000_000_000) // 1 second
        
        return mockNews
    }
    
    func getNewsItem(id: String) async throws -> NewsItem {
        // Simulate network delay
        try await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
        
        guard let newsItem = mockNews.first(where: { $0.id == id }) else {
            throw NewsError.newsNotFound
        }
        
        return newsItem
    }
}

// MARK: - News Error
enum NewsError: Error, LocalizedError {
    case newsNotFound
    case networkError
    case loadingFailed
    
    var errorDescription: String? {
        switch self {
        case .newsNotFound:
            return "News article not found"
        case .networkError:
            return "Network connection error"
        case .loadingFailed:
            return "Failed to load news"
        }
    }
}
