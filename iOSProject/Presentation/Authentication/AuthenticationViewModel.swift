import SwiftUI
import Combine

// MARK: - Authentication View Model
class AuthenticationViewModel: ObservableObject {
    // MARK: - Published Properties
    @Published var email: String = ""
    @Published var password: String = ""
    @Published var isPasswordVisible: Bool = false
    @Published var isLoading: Bool = false
    @Published var showError: Bool = false
    @Published var errorMessage: String = ""
    @Published var emailError: String?
    @Published var passwordError: String?
    
    // MARK: - Dependencies
    private let authUseCase: AuthUseCaseProtocol
    private let validationService: ValidationServiceProtocol
    private var navigationService: NavigationServiceProtocol
    
    // MARK: - Private Properties
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Initialization
    init(
        authUseCase: AuthUseCaseProtocol,
        validationService: ValidationServiceProtocol,
        navigationService: NavigationServiceProtocol
    ) {
        self.authUseCase = authUseCase
        self.validationService = validationService
        self.navigationService = navigationService
        
        setupValidation()
    }
    
    // MARK: - Public Methods
    func loginTapped() {
        guard validateForm() else { return }
        
        Task { @MainActor in
            isLoading = true
            
            do {
                let user = try await authUseCase.login(email: email, password: password)
                // Navigate to main navigation
                navigationService.navigateToMainNavigation()
            } catch {
                showErrorMessage(error.localizedDescription)
            }
            
            isLoading = false
        }
    }
    
    func googleLoginTapped() {
        // Implement Google login
        print("Google login tapped")
    }
    
    func appleLoginTapped() {
        // Implement Apple login
        print("Apple login tapped")
    }
    
    func forgotPasswordTapped() {
        // Implement forgot password
        print("Forgot password tapped")
    }
    
    func signUpTapped() {
        // Navigate to sign up screen
        print("Sign up tapped")
    }
    
    func togglePasswordVisibility() {
        isPasswordVisible.toggle()
    }

    func updateNavigationService(_ service: NavigationServiceProtocol) {
        navigationService = service
    }
    
    // MARK: - Private Methods
    private func setupValidation() {
        // Email validation
        $email
            .debounce(for: .milliseconds(500), scheduler: RunLoop.main)
            .sink { [weak self] email in
                self?.validateEmail(email)
            }
            .store(in: &cancellables)
        
        // Password validation
        $password
            .debounce(for: .milliseconds(500), scheduler: RunLoop.main)
            .sink { [weak self] password in
                self?.validatePassword(password)
            }
            .store(in: &cancellables)
    }
    
    private func validateEmail(_ email: String) {
        guard !email.isEmpty else {
            emailError = nil
            return
        }
        
        let result = validationService.validateEmail(email)
        emailError = result.errorMessage
    }
    
    private func validatePassword(_ password: String) {
        guard !password.isEmpty else {
            passwordError = nil
            return
        }
        
        let result = validationService.validatePassword(password)
        passwordError = result.errorMessage
    }
    
    private func validateForm() -> Bool {
        let emailResult = validationService.validateEmail(email)
        let passwordResult = validationService.validatePassword(password)
        
        emailError = emailResult.errorMessage
        passwordError = passwordResult.errorMessage
        
        return emailResult.isValid && passwordResult.isValid
    }
    
    private func showErrorMessage(_ message: String) {
        errorMessage = message
        showError = true
    }
}

// MARK: - Mock Implementations (temporary)
class MockAuthUseCase: AuthUseCaseProtocol {
    func login(email: String, password: String) async throws -> User {
        // Simulate network delay
        try await Task.sleep(nanoseconds: 1_000_000_000) // 1 second
        
        // Simulate login validation
        if email == "<EMAIL>" && password == "password123" {
            return User.mock
        } else {
            throw AuthError.invalidCredentials
        }
    }
    
    func logout() async throws {
        // Implement logout
    }
    
    func getCurrentUser() async -> User? {
        return nil
    }
}


