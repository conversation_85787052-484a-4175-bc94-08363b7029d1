import SwiftUI

// MARK: - Authentication View

struct AuthenticationView: View {
    @StateObject private var viewModel: AuthenticationViewModel
    @Environment(\.appColors) var colors
    @EnvironmentObject var localizationManager: LocalizationManager
    @EnvironmentObject var dependencyContainer: DependencyContainer
    @EnvironmentObject var navigationService: NavigationService

    init() {
        // Temporary initialization - will be replaced with proper DI
        self._viewModel = StateObject(wrappedValue: AuthenticationViewModel(
            authUseCase: MockAuthUseCase(),
            validationService: ValidationService(),
            navigationService: NavigationService()
        ))
    }
    
    var body: some View {
        GeometryReader { geometry in
            ScrollView {
                VStack(spacing: 32.h) {
                    Spacer(minLength: 44.h)

                    // Illustration Section
                    illustrationSection

                    // Header Section
                    headerSection

                    // Login Form
                    loginFormSection

                    // Social Login Section
                    socialLoginSection

                    // Sign Up Section
                    signUpSection

                    Spacer(minLength: 32.h)
                }
                .padding(.horizontal, 16.h)
                .frame(maxWidth: 400.h)
                .frame(minHeight: geometry.size.height)
            }
            .frame(maxWidth: .infinity)
        }
        .themedBackground()
        .alert("Error", isPresented: $viewModel.showError) {
            Button("OK") {}
        } message: {
            Text(viewModel.errorMessage)
        }
        .onAppear {
            // Update the view model with the environment navigation service
            viewModel.updateNavigationService(navigationService)
        }
    }
    
    // MARK: - Illustration Section

    private var illustrationSection: some View {
        VStack {
            Image("Illustration")
                .resizable()
                .aspectRatio(contentMode: .fit)
                .frame(width: 152.h, height: 118.h)
        }
    }
    
    // MARK: - Header Section

    private var headerSection: some View {
        VStack(spacing: 8.h) {
            Text(localizationManager.localizedString(for: "loginToAccount"))
                .headline24SemiBold()
                .themedTextColor(.primary)
                .multilineTextAlignment(.center)
            
            Text(localizationManager.localizedString(for: "welcomeBack"))
                .body14Regular()
                .themedTextColor(.secondary)
                .multilineTextAlignment(.center)
        }
    }
    
    // MARK: - Login Form Section

    private var loginFormSection: some View {
        VStack(spacing: 24.h) {
            // Email Field
            emailField
            
            // Password Field
            passwordField
            
            // Forgot Password
            forgotPasswordSection
            
            // Login Button
            loginButton
        }
    }
    
    // MARK: - Email Field

    private var emailField: some View {
        VStack(alignment: .leading, spacing: 8.h) {
            Text(localizationManager.localizedString(for: "email"))
                .body12Medium()
                .themedTextColor(.primary)
            
            CustomInputField(
                text: $viewModel.email,
                placeholder: localizationManager.localizedString(for: "enterEmail"),
                keyboardType: .emailAddress,
                leadingIcon: "envelope"
            )
            
            if let emailError = viewModel.emailError {
                Text(emailError)
                    .body12Regular()
                    .foregroundColor(colors.error)
            }
        }
    }
    
    // MARK: - Password Field

    private var passwordField: some View {
        VStack(alignment: .leading, spacing: 8.h) {
            Text(localizationManager.localizedString(for: "password"))
                .body12Medium()
                .themedTextColor(.primary)
            
            CustomInputField(
                text: $viewModel.password,
                placeholder: localizationManager.localizedString(for: "enterPassword"),
                isSecure: !viewModel.isPasswordVisible,
                
                onTrailingIconTap: {
                    viewModel.togglePasswordVisibility()
                }
            )
            
            if let passwordError = viewModel.passwordError {
                Text(passwordError)
                    .body12Regular()
                    .foregroundColor(colors.error)
            }
        }
    }
    
    // MARK: - Forgot Password Section

    private var forgotPasswordSection: some View {
        HStack {
            Spacer()
            Button(action: {
                viewModel.forgotPasswordTapped()
            }) {
                Text(localizationManager.localizedString(for: "forgotPassword"))
                    .body12SemiBold()
                    .themedTextColor(.primary)
            }
        }
    }
    
    // MARK: - Login Button

    private var loginButton: some View {
        CustomButton(
            title: localizationManager.localizedString(for: "logIn"),
            isLoading: viewModel.isLoading,
            action: viewModel.loginTapped
        )
    }
    
    // MARK: - Social Login Section

    private var socialLoginSection: some View {
        VStack(spacing: 12.h) {
            CustomButton(
                title: localizationManager.localizedString(for: "loginWithGoogle"),
                variant: .outlined,
                leadingIcon: "GoogleLogo",
                action: {
                    viewModel.googleLoginTapped()
                }
            )

            CustomButton(
                title: localizationManager.localizedString(for: "loginWithApple"),
                variant: .outlined,
                leadingIcon: "AppleLogo",
                action: {
                    viewModel.appleLoginTapped()
                }
            )
        }
    }
    
    // MARK: - Sign Up Section

    private var signUpSection: some View {
        HStack(spacing: 4.h) {
            Text(localizationManager.localizedString(for: "dontHaveAccount"))
                .body12Regular()
                .themedTextColor(.secondary)
            
            Button(action: {
                viewModel.signUpTapped()
            }) {
                Text(localizationManager.localizedString(for: "signUp"))
                    .body12SemiBold()
                    .themedTextColor(.primary)
            }
        }
    }
}

// MARK: - Preview

struct AuthenticationView_Previews: PreviewProvider {
    static var previews: some View {
        AuthenticationView()
            .environmentObject(ThemeManager())
            .environmentObject(LocalizationManager())
            .environmentObject(DependencyContainer())
            .environmentObject(NavigationService())
    }
}
