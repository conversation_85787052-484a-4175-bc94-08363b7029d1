import SwiftUI

// MARK: - Landing View
struct LandingView: View {
    @StateObject private var viewModel = LandingViewModel(newsUseCase: MockNewsUseCase())
    @Environment(\.appColors) var colors
    @EnvironmentObject var localizationManager: LocalizationManager
    
    var body: some View {
        ScrollView {
            LazyVStack(spacing: 0) {
                // Header Section
                headerSection
                
                // Hero Section
                heroSection
                
                // Partners Section
                partnersSection
                
                // Features Section
                featuresSection
                
                // CTA Section
                ctaSection
                
                // Divider
                dividerSection
                
                // Pricing Section
                pricingSection
                
                // Divider
                dividerSection
                
                // Newsletter Section
                newsletterSection
            }
        }
        .themedBackground()
        .refreshable {
            viewModel.refreshData()
        }
        .alert("Error", isPresented: $viewModel.showError) {
            Button("OK") { }
        } message: {
            Text(viewModel.errorMessage)
        }
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        VStack(spacing: 0) {
            Spacer().frame(height: 60.h)
            
            HStack(spacing: 8.h) {
                // New Feature Badge
                HStack(spacing: 8.h) {
                    Text(localizationManager.localizedString(for: "newFeature"))
                        .body12Medium()
                        .foregroundColor(Color(hex: "6840C6"))
                        .padding(.horizontal, 12.h)
                        .padding(.vertical, 4.h)
                        .background(colors.surface)
                        .clipShape(RoundedRectangle(cornerRadius: 11.h))
                    
                    Text("Personalized coaching in-app")
                        .body12Medium()
                        .foregroundColor(colors.brandPrimary)
                }
                
                Spacer()
                
                Image(systemName: "arrow.right")
                    .font(.system(size: 16))
                    .foregroundColor(Color(hex: "A855F7"))
            }
            .padding(.horizontal, 16.h)
            .padding(.vertical, 8.h)
            .background(Color(hex: "F9F5FF"))
            .clipShape(RoundedRectangle(cornerRadius: 15.h))
            .padding(.horizontal, 16.h)
        }
        .padding(.vertical, 20.h)
    }
    
    // MARK: - Hero Section
    private var heroSection: some View {
        VStack(spacing: 32.h) {
            VStack(alignment: .leading, spacing: 24.h) {
                Text(localizationManager.localizedString(for: "portfolioPerformance"))
                    .display36SemiBold()
                    .themedTextColor(.primary)
                    .lineSpacing(4)
                
                Text(localizationManager.localizedString(for: "portfolioDescription"))
                    .headline18Regular()
                    .themedTextColor(.secondary)
                    .lineSpacing(6)
            }
            .frame(maxWidth: .infinity, alignment: .leading)
            
            // App Store Buttons
            HStack(spacing: 16.h) {
                AppStoreButtonView(type: .appStore) {
                    viewModel.appStoreButtonTapped()
                }
                
                AppStoreButtonView(type: .playStore) {
                    viewModel.playStoreButtonTapped()
                }
            }
            
            // Hero Image Placeholder
            RoundedRectangle(cornerRadius: 16.h)
                .fill(colors.backgroundLight)
                .frame(height: 380.h)
                .overlay(
                    VStack {
                        Image(systemName: "iphone")
                            .font(.system(size: 60))
                            .foregroundColor(colors.brandPrimary)
                        Text("Hero Image")
                            .body14Medium()
                            .themedTextColor(.secondary)
                    }
                )
        }
        .padding(.horizontal, 16.h)
        .padding(.vertical, 32.h)
    }
    
    // MARK: - Partners Section
    private var partnersSection: some View {
        VStack(spacing: 32.h) {
            Text("Official partner of these companies")
                .title16Medium()
                .themedTextColor(.secondary)
            
            // Partner Logos Grid
            VStack(spacing: 24.h) {
                HStack(spacing: 32.h) {
                    ForEach(Array(viewModel.landingData.partnerLogos.prefix(2)), id: \.id) { partner in
                        PartnerLogoView(partner: partner)
                    }
                }
                
                HStack(spacing: 32.h) {
                    ForEach(Array(viewModel.landingData.partnerLogos.dropFirst(2).prefix(3)), id: \.id) { partner in
                        PartnerLogoView(partner: partner)
                    }
                }
                
                HStack(spacing: 32.h) {
                    ForEach(Array(viewModel.landingData.partnerLogos.dropFirst(5)), id: \.id) { partner in
                        PartnerLogoView(partner: partner)
                    }
                }
            }
        }
        .padding(.horizontal, 16.h)
        .padding(.vertical, 32.h)
        .background(colors.backgroundLight)
    }
    
    // MARK: - Features Section
    private var featuresSection: some View {
        VStack(spacing: 48.h) {
            VStack(alignment: .leading, spacing: 16.h) {
                Text("Features")
                    .body14SemiBold()
                    .foregroundColor(colors.brandPrimary)
                
                Text(localizationManager.localizedString(for: "unlockYourself"))
                    .display30SemiBold()
                    .themedTextColor(.primary)
                    .lineSpacing(4)
                
                Text("Daily personalized fitness, sleep, and recovery data delivered to you in real time with Untitled. We're changing how you move.")
                    .headline18Regular()
                    .themedTextColor(.secondary)
                    .lineSpacing(6)
            }
            .frame(maxWidth: .infinity, alignment: .leading)
            
            // Features List
            VStack(spacing: 48.h) {
                ForEach(viewModel.landingData.features) { feature in
                    FeatureItemView(feature: feature)
                }
            }
            
            // Feature Image Placeholder
            RoundedRectangle(cornerRadius: 16.h)
                .fill(colors.backgroundLight)
                .frame(height: 360.h)
                .overlay(
                    VStack {
                        Image(systemName: "apps.iphone")
                            .font(.system(size: 60))
                            .foregroundColor(colors.brandPrimary)
                        Text("Feature Mockup")
                            .body14Medium()
                            .themedTextColor(.secondary)
                    }
                )
        }
        .padding(.horizontal, 16.h)
        .padding(.vertical, 32.h)
    }

    // MARK: - CTA Section
    private var ctaSection: some View {
        VStack(spacing: 32.h) {
            VStack(spacing: 16.h) {
                Text(localizationManager.localizedString(for: "startFreeTrial"))
                    .display30SemiBold()
                    .foregroundColor(.white)
                    .lineSpacing(4)

                Text("Personal performance tracking made easy.")
                    .headline18Regular()
                    .foregroundColor(Color(hex: "E9D7FE"))
                    .lineSpacing(6)
            }

            // CTA Buttons
            HStack(spacing: 16.h) {
                AppStoreButtonView(type: .appStore) {
                    viewModel.appStoreButtonTapped()
                }

                AppStoreButtonView(type: .playStore) {
                    viewModel.playStoreButtonTapped()
                }
            }

            // CTA Image Placeholder
            RoundedRectangle(cornerRadius: 16.h)
                .fill(Color.white.opacity(0.1))
                .frame(height: 200.h)
                .overlay(
                    VStack {
                        Image(systemName: "star.fill")
                            .font(.system(size: 40))
                            .foregroundColor(.white)
                        Text("CTA Visual")
                            .body14Medium()
                            .foregroundColor(.white)
                    }
                )
        }
        .padding(.horizontal, 16.h)
        .padding(.vertical, 40.h)
        .background(Color(hex: "52379E"))
        .clipShape(RoundedRectangle(cornerRadius: 16.h))
        .padding(.horizontal, 16.h)
    }

    // MARK: - Divider Section
    private var dividerSection: some View {
        Rectangle()
            .fill(colors.borderDefault)
            .frame(height: 1)
            .padding(.horizontal, 16.h)
    }

    // MARK: - Pricing Section
    private var pricingSection: some View {
        VStack(spacing: 48.h) {
            VStack(spacing: 16.h) {
                Text("Pricing")
                    .body14SemiBold()
                    .foregroundColor(colors.brandPrimary)

                Text("Simple, transparent pricing")
                    .display30SemiBold()
                    .themedTextColor(.primary)
                    .lineSpacing(4)

                Text("We believe Untitled should be accessible to all companies, no matter the size.")
                    .headline18Regular()
                    .themedTextColor(.secondary)
                    .lineSpacing(6)
            }

            // Pricing Cards
            VStack(spacing: 32.h) {
                ForEach(viewModel.landingData.pricingPlans) { plan in
                    PricingCardView(plan: plan) {
                        viewModel.pricingPlanSelected(plan)
                    }
                }
            }
        }
        .padding(.horizontal, 16.h)
        .padding(.vertical, 48.h)
    }

    // MARK: - Newsletter Section
    private var newsletterSection: some View {
        VStack(spacing: 32.h) {
            VStack(spacing: 16.h) {
                Text("Stay up to date")
                    .display30SemiBold()
                    .themedTextColor(.primary)
                    .lineSpacing(4)

                Text("Stay updated with the latest features and releases.")
                    .headline18Regular()
                    .themedTextColor(.secondary)
                    .lineSpacing(6)
            }

            // Newsletter Signup
            VStack(spacing: 16.h) {
                HStack(spacing: 12.h) {
                    CustomInputField(
                        text: $viewModel.newsletterEmail,
                        placeholder: "Enter your email",
                        keyboardType: .emailAddress,
                        leadingIcon: "envelope"
                    )

                    CustomButton(
                        title: "Subscribe",
                        isLoading: viewModel.isSubscribing,
                        action: {
                            viewModel.subscribeToNewsletter()
                        }
                    )
                    .frame(width: 120.h)
                }

                Text("We care about your data in our privacy policy.")
                    .body12Regular()
                    .themedTextColor(.secondary)
                    .multilineTextAlignment(.center)
            }
        }
        .padding(.horizontal, 16.h)
        .padding(.vertical, 48.h)
        .background(colors.backgroundLight)
    }
}

// MARK: - Preview
struct LandingView_Previews: PreviewProvider {
    static var previews: some View {
        LandingView()
            .environmentObject(ThemeManager())
            .environmentObject(LocalizationManager())
    }
}
