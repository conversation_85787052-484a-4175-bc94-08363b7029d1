import SwiftUI

// MARK: - App Store Button Type
enum AppStoreButtonType {
    case appStore
    case playStore
    
    var downloadText: String {
        switch self {
        case .appStore:
            return "Download on the"
        case .playStore:
            return "Get it on"
        }
    }
    
    var storeText: String {
        switch self {
        case .appStore:
            return "App Store"
        case .playStore:
            return "Google Play"
        }
    }
    
    var logoIcon: String {
        switch self {
        case .appStore:
            return "AppleLogo"
        case .playStore:
            return "GoogleLogo"
        }
    }
}

// MARK: - App Store Button View
struct AppStoreButtonView: View {
    let type: AppStoreButtonType
    let action: () -> Void
    @Environment(\.appColors) var colors
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 12.h) {
                // Logo
                if UIImage(named: type.logoIcon) != nil {
                    Image(type.logoIcon)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(width: 24.h, height: 24.h)
                } else {
                    // Fallback to system icon
                    Image(systemName: type == .appStore ? "apple.logo" : "globe")
                        .font(.system(size: 24))
                        .foregroundColor(colors.onSurface)
                }
                
                // Text Content
                VStack(alignment: .leading, spacing: 2) {
                    Text(type.downloadText)
                        .caption10Regular()
                        .themedTextColor(.secondary)
                    
                    Text(type.storeText)
                        .body14Medium()
                        .themedTextColor(.primary)
                }
                
                Spacer()
            }
            .padding(.horizontal, 16.h)
            .padding(.vertical, 12.h)
            .background(colors.surface)
            .overlay(
                RoundedRectangle(cornerRadius: 8.h)
                    .stroke(colors.borderDefault, lineWidth: 1)
            )
            .clipShape(RoundedRectangle(cornerRadius: 8.h))
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Preview
struct AppStoreButtonView_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 16) {
            AppStoreButtonView(
                type: .appStore,
                action: { }
            )
            
            AppStoreButtonView(
                type: .playStore,
                action: { }
            )
        }
        .padding()
        .environmentObject(ThemeManager())
    }
}
