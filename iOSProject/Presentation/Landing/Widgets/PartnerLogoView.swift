import SwiftUI

// MARK: - Partner Logo View
struct PartnerLogoView: View {
    let partner: PartnerLogo
    @Environment(\.appColors) var colors
    
    var body: some View {
        VStack(spacing: 8.h) {
            // Logo Container
            RoundedRectangle(cornerRadius: 8.h)
                .fill(colors.backgroundLight)
                .frame(width: 80.h, height: 48.h)
                .overlay(
                    logoContent
                )
        }
    }
    
    // MARK: - Logo Content
    private var logoContent: some View {
        Group {
            if UIImage(named: partner.logoName) != nil {
                Image(partner.logoName)
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(maxWidth: 60.h, maxHeight: 32.h)
            } else {
                // Fallback to company name text
                Text(partner.companyName)
                    .body12Medium()
                    .themedTextColor(.secondary)
                    .multilineTextAlignment(.center)
            }
        }
    }
}

// MARK: - Preview
struct PartnerLogoView_Previews: PreviewProvider {
    static var previews: some View {
        HStack(spacing: 16) {
            PartnerLogoView(
                partner: PartnerLogo(logoName: "apple.logo", companyName: "Apple")
            )
            
            PartnerLogoView(
                partner: PartnerLogo(logoName: "google.logo", companyName: "Google")
            )
            
            PartnerLogoView(
                partner: PartnerLogo(logoName: "microsoft.logo", companyName: "Microsoft")
            )
        }
        .padding()
        .environmentObject(ThemeManager())
    }
}
