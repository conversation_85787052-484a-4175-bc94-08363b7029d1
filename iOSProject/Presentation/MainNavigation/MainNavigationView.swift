import SwiftUI
import UIKit

// MARK: - Main Navigation View
struct MainNavigationView: View {
    @State private var selectedTab = 0
    @State private var showingProfile = false
    @Environment(\.appColors) var colors
    @EnvironmentObject var localizationManager: LocalizationManager
    @EnvironmentObject var dependencyContainer: DependencyContainer
    
    var body: some View {
        ZStack {
            // Main Tab View
            TabView(selection: $selectedTab) {
                LandingView()
                    .tabItem {
                        EmptyView()
                    }
                    .tag(0)
                
                FavouriteView()
                    .tabItem {
                        EmptyView()
                    }
                    .tag(1)
                
                NewsView()
                    .tabItem {
                        EmptyView()
                    }
                    .tag(2)
                
                NotificationView()
                    .tabItem {
                        EmptyView()
                    }
                    .tag(3)
            }
            .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
            
            // Custom Tab Bar
            VStack {
                Spacer()
                customTabBar
            }
            
            // Profile Drawer
            if showingProfile {
                profileDrawerOverlay
            }
        }
        .themedBackground()
    }
    
    // MARK: - Custom Tab Bar
    private var customTabBar: some View {
        VStack(spacing: 0) {
            // Top border
            Rectangle()
                .fill(colors.borderDefault)
                .frame(height: 1)
            
            HStack(spacing: 0) {
                // Home Tab
                tabBarItem(
                    icon: "HouseIcon",
                    title: localizationManager.localizedString(for: "home"),
                    index: 0
                )
                
                // Favourite Tab
                tabBarItem(
                    icon: "HeartIcon",
                    title: localizationManager.localizedString(for: "favourite"),
                    index: 1
                )
                
                // News Tab
                tabBarItem(
                    icon: "newspaper",
                    title: localizationManager.localizedString(for: "news"),
                    index: 2,
                    isSystemIcon: true
                )
                
                // Notification Tab
                tabBarItem(
                    icon: "BellIcon",
                    title: localizationManager.localizedString(for: "notification"),
                    index: 3
                )
                
                // Profile Button
                profileButton
            }
            .padding(.horizontal, 16.h)
            .padding(.vertical, 8.h)
            .background(colors.surface)
        }
        .clipShape(
            .rect(
                topLeadingRadius: 8,
                topTrailingRadius: 8
            )
        )
    }
    
    // MARK: - Tab Bar Item
    private func tabBarItem(
        icon: String,
        title: String,
        index: Int,
        isSystemIcon: Bool = false
    ) -> some View {
        Button(action: {
            selectedTab = index
        }) {
            VStack(spacing: 4) {
                if isSystemIcon {
                    Image(systemName: icon)
                        .font(.system(size: 24))
                        .foregroundColor(selectedTab == index ? colors.brandPrimary : colors.secondaryText)
                } else {
                    Image(icon)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(width: 24, height: 24)
                        .foregroundColor(selectedTab == index ? colors.brandPrimary : colors.secondaryText)
                }
                
                Text(title)
                    .caption10Regular()
                    .foregroundColor(selectedTab == index ? colors.brandPrimary : colors.secondaryText)
                    .lineLimit(1)
            }
            .frame(maxWidth: .infinity)
            .frame(height: 50)
        }
    }
    
    // MARK: - Profile Button
    private var profileButton: some View {
        Button(action: {
            withAnimation(.easeInOut(duration: 0.3)) {
                showingProfile.toggle()
            }
        }) {
            VStack(spacing: 4) {
                Image(systemName: "person.circle")
                    .font(.system(size: 24))
                    .foregroundColor(showingProfile ? colors.brandPrimary : colors.secondaryText)
                
                Text("Profile")
                    .caption10Regular()
                    .foregroundColor(showingProfile ? colors.brandPrimary : colors.secondaryText)
                    .lineLimit(1)
            }
            .frame(maxWidth: .infinity)
            .frame(height: 50)
        }
    }
    
    // MARK: - Profile Drawer Overlay
    private var profileDrawerOverlay: some View {
        ZStack {
            // Background overlay
            Color.black.opacity(0.3)
                .ignoresSafeArea()
                .onTapGesture {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        showingProfile = false
                    }
                }
            
            // Profile drawer
            HStack {
                Spacer()
                
                ProfileView()
                    .frame(width: UIScreen.main.bounds.width * 0.66)
                    .background(colors.surface)
                    .transition(.move(edge: .trailing))
            }
        }
    }
}

// MARK: - Placeholder Views
// LandingView is now in its own file

struct FavouriteView: View {
    var body: some View {
        VStack {
            Text("Favourite Screen")
                .headline24SemiBold()
            Text("Favourite content will be implemented here")
                .body14Regular()
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .themedBackground()
    }
}

struct NewsView: View {
    var body: some View {
        VStack {
            Text("News Screen")
                .headline24SemiBold()
            Text("News content will be implemented here")
                .body14Regular()
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .themedBackground()
    }
}

struct NotificationView: View {
    var body: some View {
        VStack {
            Text("Notification Screen")
                .headline24SemiBold()
            Text("Notification content will be implemented here")
                .body14Regular()
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .themedBackground()
    }
}

struct ProfileView: View {
    var body: some View {
        VStack {
            Text("Profile Screen")
                .headline24SemiBold()
            Text("Profile content will be implemented here")
                .body14Regular()
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .themedBackground()
    }
}

// MARK: - Preview
struct MainNavigationView_Previews: PreviewProvider {
    static var previews: some View {
        MainNavigationView()
            .environmentObject(ThemeManager())
            .environmentObject(LocalizationManager())
            .environmentObject(DependencyContainer())
    }
}
