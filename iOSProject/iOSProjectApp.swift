//
//  iOSProjectApp.swift
//  iOSProject
//
//  Created by Apple on 08/08/2025.
//

import SwiftUI

@main
struct iOSProjectApp: App {
    @StateObject private var themeManager = ThemeManager()

    @StateObject private var dependencyContainer = DependencyContainer()

    init() {
        
    }

    var body: some Scene {
        WindowGroup {
            MainView()
                .environmentObject(themeManager)

                .environmentObject(dependencyContainer)
                .appTheme(themeManager)
                .onReceive(themeManager.$currentColors) { colors in
                    // Update environment when theme changes
                }
        }
    }
}
