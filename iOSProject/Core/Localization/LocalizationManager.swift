import SwiftUI
import Foundation

// MARK: - Supported Languages
enum SupportedLanguage: String, CaseIterable {
    case english = "en"
    case arabic = "ar"
    
    var displayName: String {
        switch self {
        case .english:
            return "English"
        case .arabic:
            return "العربية"
        }
    }
    
    var isRTL: Bool {
        return self == .arabic
    }
}

// MARK: - Localization Manager
class LocalizationManager: ObservableObject {
    @Published var currentLanguage: SupportedLanguage = .english
    @Published var isRTL: Bool = false
    
    private let userDefaults = UserDefaults.standard
    private let languageKey = "selectedLanguage"
    
    init() {
        loadLanguage()
        updateRTL()
    }
    
    // MARK: - Public Methods
    func setLanguage(_ language: SupportedLanguage) {
        currentLanguage = language
        saveLanguage()
        updateRTL()
    }
    
    func toggleLanguage() {
        let newLanguage: SupportedLanguage = currentLanguage == .english ? .arabic : .english
        setLanguage(newLanguage)
    }
    
    // MARK: - Localized Strings
    func localizedString(for key: String) -> String {
        return LocalizedStrings.string(for: key, language: currentLanguage)
    }
    
    // MARK: - Private Methods
    private func loadLanguage() {
        if let savedLanguage = userDefaults.string(forKey: languageKey),
           let language = SupportedLanguage(rawValue: savedLanguage) {
            currentLanguage = language
        }
    }
    
    private func saveLanguage() {
        userDefaults.set(currentLanguage.rawValue, forKey: languageKey)
    }
    
    private func updateRTL() {
        isRTL = currentLanguage.isRTL
    }
}

// MARK: - Localized Strings
struct LocalizedStrings {
    
    // English Strings
    private static let englishStrings: [String: String] = [
        "back": "Back",
        "darkMode": "Dark Mode",
        "editProfile": "Edit Profile",
        "address": "Address",
        "history": "History",
        "complain": "Complain",
        "referral": "Referral",
        "aboutUs": "About Us",
        "settings": "Settings",
        "helpAndSupport": "Help and Support",
        "logout": "Logout",
        "selectLanguage": "Select Language",
        "english": "English",
        "arabic": "العربية",
        "portfolioPerformance": "Portfolio performance tracking made easy",
        "portfolioDescription": "Powerful, self-serve product and growth analytics to help you convert, engage, and retain more users. Trusted by over 4,000 startups.",
        "appStore": "App Store",
        "playStore": "Play Store",
        "language": "Language",
        "newFeature": "New",
        "startFreeTrial": "Start your free trial",
        "unlockYourself": "Unlock yourself",
        "getStarted": "Get started",
        "logIn": "Log in",
        "signUp": "Sign up",
        "analyticsFromFuture": "Analytics that feels like it's from the future",
        "join4000Companies": "Join 4,000+ companies already growing",
        
        // Authentication Screen
        "loginToAccount": "Login to you account",
        "welcomeBack": "Welcome back! Please enter your details.",
        "email": "Email",
        "enterEmail": "Enter your email",
        "password": "Password",
        "enterPassword": "Enter password",
        "forgotPassword": "Forgot password?",
        "loginWithGoogle": "Log in with Google",
        "loginWithApple": "Log in with Apple",
        "dontHaveAccount": "Don't have an account?",
        
        // Navigation
        "home": "Home",
        "favourite": "Favourite",
        "news": "News",
        "notification": "Notification"
    ]
    
    // Arabic Strings
    private static let arabicStrings: [String: String] = [
        "back": "رجوع",
        "darkMode": "الوضع المظلم",
        "editProfile": "تعديل الملف الشخصي",
        "address": "العنوان",
        "history": "التاريخ",
        "complain": "شكوى",
        "referral": "إحالة",
        "aboutUs": "معلومات عنا",
        "settings": "الإعدادات",
        "helpAndSupport": "المساعدة والدعم",
        "logout": "تسجيل الخروج",
        "selectLanguage": "اختر اللغة",
        "english": "English",
        "arabic": "العربية",
        "portfolioPerformance": "تتبع أداء المحفظة أصبح سهلاً",
        "portfolioDescription": "تحليلات قوية للمنتج والنمو لمساعدتك على التحويل والمشاركة والاحتفاظ بمزيد من المستخدمين. موثوق به من قبل أكثر من 4000 شركة ناشئة.",
        "appStore": "متجر التطبيقات",
        "playStore": "متجر بلاي",
        "language": "اللغة",
        "newFeature": "جديد",
        "startFreeTrial": "ابدأ تجربتك المجانية",
        "unlockYourself": "اطلق العنان لنفسك",
        "getStarted": "ابدأ",
        "logIn": "تسجيل الدخول",
        "signUp": "إنشاء حساب",
        "analyticsFromFuture": "تحليلات تبدو وكأنها من المستقبل",
        "join4000Companies": "انضم إلى أكثر من 4000 شركة تنمو بالفعل",
        
        // Authentication Screen
        "loginToAccount": "تسجيل الدخول إلى حسابك",
        "welcomeBack": "مرحباً بعودتك! يرجى إدخال التفاصيل الخاصة بك.",
        "email": "البريد الإلكتروني",
        "enterEmail": "أدخل بريدك الإلكتروني",
        "password": "كلمة المرور",
        "enterPassword": "أدخل كلمة المرور",
        "forgotPassword": "نسيت كلمة المرور؟",
        "loginWithGoogle": "تسجيل الدخول باستخدام Google",
        "loginWithApple": "تسجيل الدخول باستخدام Apple",
        "dontHaveAccount": "ليس لديك حساب؟",
        
        // Navigation
        "home": "الرئيسية",
        "favourite": "المفضلة",
        "news": "الأخبار",
        "notification": "الإشعارات"
    ]
    
    static func string(for key: String, language: SupportedLanguage) -> String {
        let strings = language == .english ? englishStrings : arabicStrings
        return strings[key] ?? key
    }
}

// MARK: - Localization Environment Key
struct LocalizationEnvironmentKey: EnvironmentKey {
    static let defaultValue = LocalizationManager()
}

extension EnvironmentValues {
    var localizationManager: LocalizationManager {
        get { self[LocalizationEnvironmentKey.self] }
        set { self[LocalizationEnvironmentKey.self] = newValue }
    }
}

// MARK: - Localized Text View
struct LocalizedText: View {
    let key: String
    @EnvironmentObject var localizationManager: LocalizationManager
    
    init(_ key: String) {
        self.key = key
    }
    
    var body: some View {
        Text(localizationManager.localizedString(for: key))
    }
}

// MARK: - RTL Support
struct RTLAwareHStack<Content: View>: View {
    @EnvironmentObject var localizationManager: LocalizationManager
    let alignment: VerticalAlignment
    let spacing: CGFloat?
    let content: () -> Content
    
    init(alignment: VerticalAlignment = .center, spacing: CGFloat? = nil, @ViewBuilder content: @escaping () -> Content) {
        self.alignment = alignment
        self.spacing = spacing
        self.content = content
    }
    
    var body: some View {
        HStack(alignment: alignment, spacing: spacing) {
            content()
        }
        .environment(\.layoutDirection, localizationManager.isRTL ? .rightToLeft : .leftToRight)
    }
}

// MARK: - View Extensions for Localization
extension View {
    func rtlAware() -> some View {
        self.environment(\.layoutDirection, LocalizationManager().isRTL ? .rightToLeft : .leftToRight)
    }
}
