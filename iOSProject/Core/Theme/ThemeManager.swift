import SwiftUI
import Combine

// MARK: - Theme Manager
class ThemeManager: ObservableObject {
    @Published var isDarkMode: Bool = false
    @Published var currentColors: AppColorsProtocol = AppLightColors()
    
    private let userDefaults = UserDefaults.standard
    private let themeKey = "isDarkMode"
    
    init() {
        loadTheme()
        updateColors()
    }
    
    // MARK: - Public Methods
    func toggleTheme() {
        isDarkMode.toggle()
        saveTheme()
        updateColors()
    }
    
    func setTheme(_ isDark: Bool) {
        isDarkMode = isDark
        saveTheme()
        updateColors()
    }
    
    // MARK: - Private Methods
    private func loadTheme() {
        isDarkMode = userDefaults.bool(forKey: themeKey)
    }
    
    private func saveTheme() {
        userDefaults.set(isDarkMode, forKey: themeKey)
    }
    
    private func updateColors() {
        currentColors = isDarkMode ? AppDarkColors() : AppLightColors()
    }
}

// MARK: - Theme Environment Key
struct ThemeEnvironmentKey: EnvironmentKey {
    static let defaultValue = ThemeManager()
}

extension EnvironmentValues {
    var themeManager: ThemeManager {
        get { self[ThemeEnvironmentKey.self] }
        set { self[ThemeEnvironmentKey.self] = newValue }
    }
}

// MARK: - App Theme
struct AppTheme {
    let colors: AppColorsProtocol
    let typography = AppTypography.self
    
    init(colors: AppColorsProtocol) {
        self.colors = colors
    }
    
    static let light = AppTheme(colors: AppLightColors())
    static let dark = AppTheme(colors: AppDarkColors())
}

// MARK: - Theme Environment Key for Colors
struct AppColorsEnvironmentKey: EnvironmentKey {
    static let defaultValue: AppColorsProtocol = AppLightColors()
}

extension EnvironmentValues {
    var appColors: AppColorsProtocol {
        get { self[AppColorsEnvironmentKey.self] }
        set { self[AppColorsEnvironmentKey.self] = newValue }
    }
}

// MARK: - View Extensions for Theme
extension View {
    func appTheme(_ themeManager: ThemeManager) -> some View {
        self
            .environment(\.themeManager, themeManager)
            .environment(\.appColors, themeManager.currentColors)
            .preferredColorScheme(themeManager.isDarkMode ? .dark : .light)
    }
}

// MARK: - Color Scheme Modifier
struct AppColorScheme: ViewModifier {
    @EnvironmentObject var themeManager: ThemeManager
    
    func body(content: Content) -> some View {
        content
            .environment(\.appColors, themeManager.currentColors)
            .preferredColorScheme(themeManager.isDarkMode ? .dark : .light)
    }
}

extension View {
    func appColorScheme() -> some View {
        self.modifier(AppColorScheme())
    }
}

// MARK: - Theme-aware Background
struct ThemedBackground: ViewModifier {
    @Environment(\.appColors) var colors
    
    func body(content: Content) -> some View {
        content
            .background(colors.background)
    }
}

extension View {
    func themedBackground() -> some View {
        self.modifier(ThemedBackground())
    }
}

// MARK: - Theme-aware Text Color
struct ThemedTextColor: ViewModifier {
    @Environment(\.appColors) var colors
    let colorType: TextColorType
    
    enum TextColorType {
        case primary
        case secondary
        case onSurface
        case onBackground
        case error
    }
    
    func body(content: Content) -> some View {
        content
            .foregroundColor(textColor)
    }
    
    private var textColor: Color {
        switch colorType {
        case .primary:
            return colors.primaryText
        case .secondary:
            return colors.secondaryText
        case .onSurface:
            return colors.onSurface
        case .onBackground:
            return colors.onBackground
        case .error:
            return colors.error
        }
    }
}

extension View {
    func themedTextColor(_ type: ThemedTextColor.TextColorType = .primary) -> some View {
        self.modifier(ThemedTextColor(colorType: type))
    }
}

// MARK: - Responsive Sizing
struct ResponsiveSize {
    static func width(_ percentage: CGFloat) -> CGFloat {
        return UIScreen.main.bounds.width * percentage
    }
    
    static func height(_ percentage: CGFloat) -> CGFloat {
        return UIScreen.main.bounds.height * percentage
    }
    
    static func size(_ value: CGFloat) -> CGFloat {
        // Scale factor based on screen width (375 is the base width from Flutter)
        let scaleFactor = UIScreen.main.bounds.width / 375.0
        return value * scaleFactor
    }
}

// MARK: - Size Extensions (matching Flutter .h extension)
extension CGFloat {
    var h: CGFloat {
        return ResponsiveSize.size(self)
    }
    
    var fSize: CGFloat {
        return ResponsiveSize.size(self)
    }
}

extension Int {
    var h: CGFloat {
        return ResponsiveSize.size(CGFloat(self))
    }
    
    var fSize: CGFloat {
        return ResponsiveSize.size(CGFloat(self))
    }
}

extension Double {
    var h: CGFloat {
        return ResponsiveSize.size(CGFloat(self))
    }
    
    var fSize: CGFloat {
        return ResponsiveSize.size(CGFloat(self))
    }
}
