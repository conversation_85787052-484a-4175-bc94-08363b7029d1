import SwiftUI

// MARK: - Color Extensions
extension Color {
    init(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            (a, r, g, b) = (1, 1, 1, 0)
        }
        
        self.init(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue:  Double(b) / 255,
            opacity: Double(a) / 255
        )
    }
}

// MARK: - Light Theme Colors
struct LightThemeColors {
    // Primary Colors
    static let primary = Color(hex: "7E56D8")
    static let onPrimary = Color.white
    static let secondary = Color(hex: "52379E")
    static let onSecondary = Color.white
    
    // Surface Colors
    static let surface = Color.white
    static let onSurface = Color(hex: "161616")
    static let background = Color.white
    static let onBackground = Color(hex: "161616")
    
    // Error Colors
    static let error = Color(hex: "FF5252")
    static let onError = Color.white
    
    // Custom App Colors
    static let primaryText = Color(hex: "161616")
    static let secondaryText = Color(hex: "525252")
    static let brandPrimary = Color(hex: "7E56D8")
    static let borderDefault = Color(hex: "CFD4DC")
    static let backgroundLight = Color(hex: "F4F4F4")
    static let backgroundGray = Color(hex: "E0E0E0")
    
    // Additional Custom Colors
    static let colorF9F5FF = Color(hex: "F9F5FF")
    static let color6840C6 = Color(hex: "6840C6")
    static let color0F1728 = Color(hex: "0F1728")
    static let color667084 = Color(hex: "667084")
    static let colorA6A6A6 = Color(hex: "A6A6A6")
    static let colorF8F9FB = Color(hex: "F8F9FB")
    static let colorE9D7FE = Color(hex: "E9D7FE")
    static let colorEAECF0 = Color(hex: "EAECF0")
    static let color422F7D = Color(hex: "422F7D")
    static let colorF2F3F6 = Color(hex: "F2F3F6")
    static let colorE5E7EB = Color(hex: "D0D5DD")
    static let color667085 = Color(hex: "667085")
    static let colorF4EBFF = Color(hex: "F4EBFF")
    static let colorD0FADF = Color(hex: "D0FADF")
    static let color101828 = Color(hex: "101828")
    
    // Shadow Color
    static let shadowColor = Color(hex: "101828").opacity(0.05)
}

// MARK: - Dark Theme Colors
struct DarkThemeColors {
    // Primary Colors
    static let primary = Color(hex: "7E56D8")
    static let onPrimary = Color.white
    static let secondary = Color(hex: "52379E")
    static let onSecondary = Color.white
    
    // Surface Colors
    static let surface = Color(hex: "1E1E1E")
    static let onSurface = Color(hex: "E5E5E5")
    static let background = Color(hex: "1E1E1E")
    static let onBackground = Color(hex: "E5E5E5")
    
    // Error Colors
    static let error = Color(hex: "FF5252")
    static let onError = Color.white
    
    // Custom App Colors (Dark variants)
    static let primaryText = Color(hex: "E5E5E5")
    static let secondaryText = Color(hex: "B3B3B3")
    static let brandPrimary = Color(hex: "7E56D8")
    static let borderDefault = Color(hex: "404040")
    static let backgroundLight = Color(hex: "2A2A2A")
    static let backgroundGray = Color(hex: "404040")
    
    // Additional Custom Colors (Dark variants)
    static let colorF9F5FF = Color(hex: "2A2A2A")
    static let color6840C6 = Color(hex: "9B7EE8")
    static let color0F1728 = Color(hex: "E5E5E5")
    static let color667084 = Color(hex: "B3B3B3")
    static let colorA6A6A6 = Color(hex: "6B7280")
    static let colorF8F9FB = Color(hex: "2D2D2D")
    static let colorE9D7FE = Color(hex: "4A4A4A")
    static let colorEAECF0 = Color(hex: "404040")
    static let color422F7D = Color(hex: "7E56D8")
    static let colorF2F3F6 = Color(hex: "2A2A2A")
    static let colorE5E7EB = Color(hex: "404040")
    static let color667085 = Color(hex: "B3B3B3")
    static let colorF4EBFF = Color(hex: "2A2A2A")
    static let colorD0FADF = Color(hex: "2A2A2A")
    static let color101828 = Color(hex: "E5E5E5")
    
    // Shadow Color
    static let shadowColor = Color.black.opacity(0.3)
}

// MARK: - App Colors Protocol
protocol AppColorsProtocol {
    var primary: Color { get }
    var onPrimary: Color { get }
    var secondary: Color { get }
    var onSecondary: Color { get }
    var surface: Color { get }
    var onSurface: Color { get }
    var background: Color { get }
    var onBackground: Color { get }
    var error: Color { get }
    var onError: Color { get }
    var primaryText: Color { get }
    var secondaryText: Color { get }
    var brandPrimary: Color { get }
    var borderDefault: Color { get }
    var backgroundLight: Color { get }
    var backgroundGray: Color { get }
    var shadowColor: Color { get }
}

// MARK: - Light Colors Implementation
struct AppLightColors: AppColorsProtocol {
    let primary = LightThemeColors.primary
    let onPrimary = LightThemeColors.onPrimary
    let secondary = LightThemeColors.secondary
    let onSecondary = LightThemeColors.onSecondary
    let surface = LightThemeColors.surface
    let onSurface = LightThemeColors.onSurface
    let background = LightThemeColors.background
    let onBackground = LightThemeColors.onBackground
    let error = LightThemeColors.error
    let onError = LightThemeColors.onError
    let primaryText = LightThemeColors.primaryText
    let secondaryText = LightThemeColors.secondaryText
    let brandPrimary = LightThemeColors.brandPrimary
    let borderDefault = LightThemeColors.borderDefault
    let backgroundLight = LightThemeColors.backgroundLight
    let backgroundGray = LightThemeColors.backgroundGray
    let shadowColor = LightThemeColors.shadowColor
}

// MARK: - Dark Colors Implementation
struct AppDarkColors: AppColorsProtocol {
    let primary = DarkThemeColors.primary
    let onPrimary = DarkThemeColors.onPrimary
    let secondary = DarkThemeColors.secondary
    let onSecondary = DarkThemeColors.onSecondary
    let surface = DarkThemeColors.surface
    let onSurface = DarkThemeColors.onSurface
    let background = DarkThemeColors.background
    let onBackground = DarkThemeColors.onBackground
    let error = DarkThemeColors.error
    let onError = DarkThemeColors.onError
    let primaryText = DarkThemeColors.primaryText
    let secondaryText = DarkThemeColors.secondaryText
    let brandPrimary = DarkThemeColors.brandPrimary
    let borderDefault = DarkThemeColors.borderDefault
    let backgroundLight = DarkThemeColors.backgroundLight
    let backgroundGray = DarkThemeColors.backgroundGray
    let shadowColor = DarkThemeColors.shadowColor
}
