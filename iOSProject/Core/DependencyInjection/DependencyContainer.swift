import SwiftUI
import Foundation

// MARK: - Dependency Container
class DependencyContainer: ObservableObject {
    
    // MARK: - Repositories
    lazy var authRepository: AuthRepositoryProtocol = {
        return MockAuthRepository()
    }()
    
    lazy var userRepository: UserRepositoryProtocol = {
        return MockUserRepository()
    }()
    
    lazy var newsRepository: NewsRepositoryProtocol = {
        return MockNewsRepository()
    }()
    
    lazy var notificationRepository: NotificationRepositoryProtocol = {
        return MockNotificationRepository()
    }()
    
    // MARK: - Use Cases
    lazy var authUseCase: AuthUseCaseProtocol = {
        return AuthUseCase(repository: authRepository)
    }()
    
    lazy var userUseCase: UserUseCaseProtocol = {
        return UserUseCase(repository: userRepository)
    }()
    
    lazy var newsUseCase: NewsUseCaseProtocol = {
        return NewsUseCase(repository: newsRepository)
    }()
    
    lazy var notificationUseCase: NotificationUseCaseProtocol = {
        return NotificationUseCase(repository: notificationRepository)
    }()
    
    // MARK: - Services
    lazy var validationService: ValidationServiceProtocol = {
        return ValidationService()
    }()
    
    lazy var navigationService: NavigationServiceProtocol = {
        return NavigationService()
    }()
    
    // MARK: - View Models Factory
    func makeAuthenticationViewModel() -> AuthenticationViewModel {
        return AuthenticationViewModel(
            authUseCase: authUseCase,
            validationService: validationService,
            navigationService: navigationService
        )
    }
    
    func makeLandingViewModel() -> LandingViewModel {
        return LandingViewModel(
            newsUseCase: newsUseCase
        )
    }
    
    func makeProfileViewModel() -> ProfileViewModel {
        return ProfileViewModel(
            userUseCase: userUseCase,
            authUseCase: authUseCase
        )
    }
    
    func makeFavouriteViewModel() -> FavouriteViewModel {
        return FavouriteViewModel(
            userUseCase: userUseCase
        )
    }
    
    func makeNewsViewModel() -> NewsViewModel {
        return NewsViewModel(
            newsUseCase: newsUseCase
        )
    }
    
    func makeNotificationViewModel() -> NotificationViewModel {
        return NotificationViewModel(
            notificationUseCase: notificationUseCase
        )
    }
}

// MARK: - Dependency Container Environment Key
struct DependencyContainerEnvironmentKey: EnvironmentKey {
    static let defaultValue = DependencyContainer()
}

extension EnvironmentValues {
    var dependencyContainer: DependencyContainer {
        get { self[DependencyContainerEnvironmentKey.self] }
        set { self[DependencyContainerEnvironmentKey.self] = newValue }
    }
}

// MARK: - View Extensions for Dependency Injection
extension View {
    func inject(_ container: DependencyContainer) -> some View {
        self.environmentObject(container)
    }
}

// MARK: - Protocol Definitions (to be implemented in respective files)

// Auth Repository Protocol
protocol AuthRepositoryProtocol {
    func login(email: String, password: String) async throws -> User
    func logout() async throws
    func getCurrentUser() async -> User?
}

// User Repository Protocol
protocol UserRepositoryProtocol {
    func getUser(id: String) async throws -> User
    func updateUser(_ user: User) async throws -> User
    func getFavourites() async throws -> [String]
    func addToFavourites(itemId: String) async throws
    func removeFromFavourites(itemId: String) async throws
}

// News Repository Protocol
protocol NewsRepositoryProtocol {
    func getNews() async throws -> [NewsItem]
    func getNewsItem(id: String) async throws -> NewsItem
}

// Notification Repository Protocol
protocol NotificationRepositoryProtocol {
    func getNotifications() async throws -> [NotificationItem]
    func markAsRead(id: String) async throws
    func markAllAsRead() async throws
}

// Use Case Protocols
protocol AuthUseCaseProtocol {
    func login(email: String, password: String) async throws -> User
    func logout() async throws
    func getCurrentUser() async -> User?
}

protocol UserUseCaseProtocol {
    func getUser(id: String) async throws -> User
    func updateUser(_ user: User) async throws -> User
    func getFavourites() async throws -> [String]
    func toggleFavourite(itemId: String) async throws
}

protocol NewsUseCaseProtocol {
    func getNews() async throws -> [NewsItem]
    func getNewsItem(id: String) async throws -> NewsItem
}

protocol NotificationUseCaseProtocol {
    func getNotifications() async throws -> [NotificationItem]
    func markAsRead(id: String) async throws
    func markAllAsRead() async throws
}

// Service Protocols
protocol ValidationServiceProtocol {
    func validateEmail(_ email: String) -> ValidationResult
    func validatePassword(_ password: String) -> ValidationResult
}

protocol NavigationServiceProtocol {
    func navigateToMainNavigation()
    func navigateToAuthentication()
    func navigateBack()
}

// MARK: - Validation Result
enum ValidationResult {
    case valid
    case invalid(String)
    
    var isValid: Bool {
        switch self {
        case .valid:
            return true
        case .invalid:
            return false
        }
    }
    
    var errorMessage: String? {
        switch self {
        case .valid:
            return nil
        case .invalid(let message):
            return message
        }
    }
}
